import { paraglideVitePlugin } from "@inlang/paraglide-js";
import tailwindcss from "@tailwindcss/vite";
import { tanstackStart } from "@tanstack/react-start/plugin/vite";
import { defineConfig } from "vite";
import tsConfigPaths from "vite-tsconfig-paths";

export default defineConfig({
  plugins: [
    paraglideVitePlugin({
      project: "./project.inlang",
      outdir: "./src/paraglide",
      outputStructure: "message-modules",
      cookieName: "PARAGLIDE_LOCALE",
      strategy: ["cookie", "url", "preferredLanguage", "baseLocale"],
    }),
    tsConfigPaths({
      projects: ["./tsconfig.json"],
    }),
    tailwindcss(),
    tanstackStart({
      target: "cloudflare-pages",
    }),
  ],
  define: {
    global: "globalThis",
    // Polyfill Buffer for browser environment
    Buffer: ["buffer", "Buffer"],
  },
  optimizeDeps: {
    exclude: ["@tanstack/start-client-core"],
  },
  build: {
    rollupOptions: {
      onwarn(warning, warn) {
        if (warning.code === "MODULE_LEVEL_DIRECTIVE") {
          return;
        }

        warn(warning);
      },
      onLog(level, log, handler) {
        if (log.code === "MODULE_LEVEL_DIRECTIVE") {
          return;
        }

        handler(level, log);
      },
    },
  },
});
