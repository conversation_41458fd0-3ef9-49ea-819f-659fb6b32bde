import { useQuery } from "@tanstack/react-query";
import { User } from "@/types/auth";
import { API_URL, req } from "@/utils/api";
import {
  COURSE_PERMISSION,
  Department,
  PERMISSION,
} from "./../types/organization";

// group
export const getGroups = () => {
  return req<Department[]>(`${API_URL}/api/v1/admin/groups`, {
    withAuth: true,
  });
};

export const useGroups = () => {
  return useQuery({
    queryKey: ["admin-groups"],
    queryFn: async () => {
      try {
        const departments = await getGroups();
        return departments || [];
      } catch (error) {
        console.error("Error fetching departments:", error);
        return [];
      }
    },
  });
};

export const createGroup = (params: Omit<Department, "id" | "users">) => {
  return req(`${API_URL}/api/v1/admin/groups`, {
    method: "POST",
    withAuth: true,
    body: JSON.stringify(params),
  });
};

export const updateGroup = (params: Omit<Department, "users" | "name">) => {
  return req(`${API_URL}/api/v1/admin/groups/${params.id}`, {
    method: "PATCH",
    withAuth: true,
    body: JSON.stringify(params),
  });
};

export const deleteGroup = (groupId: number) => {
  return req(`${API_URL}/api/v1/admin/groups/${groupId}`, {
    method: "DELETE",
    withAuth: true,
  });
};

export const addMembersToGroup = (params: {
  group_id: string;
  user_ids: string[];
}) => {
  return req(`${API_URL}/api/v1/admin/groups/${params.group_id}/members`, {
    method: "POST",
    withAuth: true,
    body: JSON.stringify(params.user_ids),
  });
};

// user

export const importUsers = (params: { name: string; email: string }[]) => {
  return req<User[]>(`${API_URL}/api/v1/admin/users/import`, {
    method: "POST",
    withAuth: true,
    body: JSON.stringify(params),
  });
};

export const getUsers = () => {
  return req<User[]>(`${API_URL}/api/v1/admin/users`, {
    withAuth: true,
  });
};

export const useUsers = () => {
  return useQuery({
    queryKey: ["admin-users"],
    queryFn: async () => {
      try {
        const users = await getUsers();
        return users || [];
      } catch (error) {
        console.error("Error fetching users:", error);
        return [];
      }
    },
  });
};

export const deleteUser = (userIds: string[]) => {
  return req(`${API_URL}/api/v1/admin/users`, {
    method: "DELETE",
    withAuth: true,
    body: JSON.stringify(userIds),
  });
};

export const createUser = (params: { name: string; email: string }) => {
  return req<User[]>(`${API_URL}/api/v1/admin/users/import`, {
    method: "POST",
    withAuth: true,
    body: JSON.stringify([params]), // Wrap in array since import endpoint expects array
  });
};

// course

export const getCoursePermissions = (courseId: string) => {
  return req(`${API_URL}/api/v1/admin/courses/${courseId}/permissions`, {
    withAuth: true,
  });
};

export const useCoursePermissions = (courseId: string) => {
  return useQuery({
    queryKey: ["course-permissions", courseId],
    queryFn: () => getCoursePermissions(courseId),
    enabled: !!courseId,
  });
};

export const addCoursePermission = (
  params: {
    course_id: string;
    group_id: string;
    permission: COURSE_PERMISSION;
  }[],
) => {
  return req(`${API_URL}/api/v1/admin/courses/permissions/add`, {
    method: "POST",
    withAuth: true,
    body: JSON.stringify(params),
  });
};

export const removeCoursePermission = (
  params: {
    course_id: string;
    group_id: string;
    permission: COURSE_PERMISSION;
  }[],
) => {
  return req(`${API_URL}/api/v1/admin/courses/permissions/remove`, {
    method: "POST",
    withAuth: true,
    body: JSON.stringify(params),
  });
};
