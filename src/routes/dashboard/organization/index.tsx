import { createFileRoute } from "@tanstack/react-router";
import { Palette, Shield } from "lucide-react";
import { useState } from "react";
import {
  type BrandColors,
  BrandingSettings,
  DepartmentManagement,
} from "@/components/organization";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

export const Route = createFileRoute("/dashboard/organization/")({
  component: OrganizationSettings,
});

/**
 * Main organization settings page component
 * Handles department management and branding configuration
 */
function OrganizationSettings() {
  const [activeTab, setActiveTab] = useState("permissions");

  // Department management is handled directly inside the DepartmentManagement component

  /**
   * Handle branding updates
   */
  const handleBrandingUpdate = (colors: BrandColors) => {
    // TODO: Implement API call to update brand colors
    console.log("Updating brand colors:", colors);
  };

  /**
   * Handle logo upload
   */
  const handleLogoUpdate = (logoFile: File) => {
    // TODO: Implement logo upload API call
    console.log("Uploading logo:", logoFile.name);
  };

  return (
    <div className="flex h-screen bg-background">
      <div className="flex flex-1 flex-col overflow-hidden">
        {/* Page Header */}
        <div className="border-b bg-background px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="font-bold text-2xl text-foreground">
                Cài đặt Tổ chức
              </h1>
              <p className="text-muted-foreground">
                Cấu hình tổ chức và phân quyền hệ thống
              </p>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <main className="flex-1 overflow-y-auto p-6">
          <Tabs
            value={activeTab}
            onValueChange={setActiveTab}
            className="space-y-6"
          >
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger
                value="permissions"
                className="flex cursor-pointer items-center gap-2"
              >
                <Shield className="h-4 w-4" />
                Phân quyền
              </TabsTrigger>
              <TabsTrigger
                value="branding"
                className="flex cursor-pointer items-center gap-2"
              >
                <Palette className="h-4 w-4" />
                Thương hiệu
              </TabsTrigger>
            </TabsList>

            {/* Permissions Tab */}
            <TabsContent value="permissions" className="space-y-6">
              <DepartmentManagement />
            </TabsContent>

            {/* Branding Tab */}
            <TabsContent value="branding" className="space-y-6">
              <BrandingSettings
                onBrandingUpdate={handleBrandingUpdate}
                onLogoUpdate={handleLogoUpdate}
              />
            </TabsContent>
          </Tabs>
        </main>
      </div>
    </div>
  );
}
