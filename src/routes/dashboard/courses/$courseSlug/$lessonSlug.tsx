import { useSuspenseQuery } from "@tanstack/react-query";
import { createFileRoute, useNavigate } from "@tanstack/react-router";
import {
  ArrowLeft,
  CheckCircle,
  ChevronDown,
  FileText,
  Gamepad2,
  Loader2,
  Plus,
  <PERSON>rk<PERSON>,
  Trophy,
} from "lucide-react";
import { useCallback, useEffect, useState } from "react";
import { toast } from "sonner";
import SectionCard from "@/components/SectionCard";
import SectionThumbnail from "@/components/SectionThumnail";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Switch } from "@/components/ui/switch";
import {
  createSection,
  DeleteSection,
  updateLesson,
  updateSection,
  useLesson,
  useSections,
} from "@/services/instructor";
import { Section } from "@/types/lessons";

export const Route = createFileRoute(
  "/dashboard/courses/$courseSlug/$lessonSlug",
)({
  component: RouteComponent,
});

function debounce<T extends (...args: unknown[]) => void>(
  func: T,
  wait: number,
) {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

function RouteComponent() {
  const { lessonSlug, courseSlug } = Route.useParams();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const {
    data: lesson,
    isLoading: loadingLesson,
    refetch: refetchLesson,
  } = useSuspenseQuery(useLesson(lessonSlug));
  const {
    data: sectionList,
    isLoading: loadingSection,
    refetch,
  } = useSections(lesson.id);
  const [saving, setSaving] = useState(false);
  const [changingIndex, setChangingIndex] = useState(false);
  const [sections, setSections] = useState<Section[]>([]);
  const [selectedSection, setSelectedSection] = useState(0);

  useEffect(() => {
    setLoading(loadingLesson && loadingSection);
  }, [loadingLesson, loadingSection]);

  const setSectionsContent = (sections: Section[], index: number) => {
    setSaving(true);
    setSections(sections);
    updateSectionContent(sections[index]);
  };

  const updateSectionContent = useCallback(
    debounce(async (section: Section) => {
      if (!section.id) {
        await createSection({ section: section, categories: [] });
      }
      await updateSection({
        id: section.id,
        section: { ...section, content: section.content },
      });
      setSaving(false);
    }, 1000),
    [],
  );

  useEffect(() => {
    if (!sectionList || loadingSection) return;
    setSections([...sectionList]);
  }, [sectionList, loadingSection]);

  const removeSection = async (index: number) => {
    if (!sections[index].id) {
      return;
    }
    const isConfirmed = window.confirm(
      `Are you sure you want to remove section #${index + 1}?`,
    );

    if (isConfirmed) {
      try {
        await Promise.all([
          DeleteSection({ id: sections[index].id }),
          sections.map((section, idx) => {
            if (idx > index)
              return updateSection({
                id: section.id,
                section: {
                  ...section,
                  ordinal_index: section.ordinal_index - 1,
                },
              });
          }),
        ]);
        toast.success("Section deleted successfully");
        refetch();
      } catch (e) {
        toast.error(`Delete section failed: ${e.toString()}`);
      }
    }
  };

  const resort = () => {
    sections.map((section, idx) => {
      return updateSection({
        id: section.id,
        section: { ...section, ordinal_index: idx + 1 },
      });
    });
  };

  const changeIndex = async (index: number, val: number) => {
    setChangingIndex(true);
    const fromSection = sections[index];
    const toSection = sections[index + val];
    await Promise.all([
      updateSection({
        id: fromSection.id,
        section: {
          ...fromSection,
          ordinal_index: fromSection.ordinal_index + val,
        },
      }),
      updateSection({
        id: toSection.id,
        section: { ...toSection, ordinal_index: fromSection.ordinal_index },
      }),
    ]);
    toast.success("Section moved successfully");
    refetch();
    setChangingIndex(false);
  };

  const addSections = async (sectionType: string) => {
    try {
      const emptySection = {
        id: crypto.randomUUID(),
        lesson_id: lesson.id,
        type: sectionType,
        content: "",
        ordinal_index: sections.length + 1,
      };
      if (sectionType === "TEXT") emptySection.content = `{}`;
      await createSection({ section: emptySection as Section, categories: [] });
      toast.success("Section created successfully");
      refetch();
    } catch (e) {
      toast.error(`Section create failed: ${e.toString()}`);
    }
  };

  const setPublished = (isPublished) => {
    updateLesson({
      id: lesson.id,
      lesson: {
        ...lesson,
        published_at: isPublished ? new Date().toISOString() : null,
      },
    })
      .then(() => {
        toast.success(
          `Lesson ${isPublished ? "published" : "unpublished"} successfully`,
        );
        refetchLesson();
      })
      .catch((e) => {
        toast.error(`Failed to publish lesson: ${e.toString()}`);
      });
  };

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="mx-auto max-w-7xl">
        {/* Header Section */}
        <div className="mb-8 overflow-hidden bg-white p-6 shadow-sm">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="flex items-center gap-3">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() =>
                    navigate({ to: `/dashboard/courses/${courseSlug}` })
                  }
                  className="flex items-center gap-2 text-gray-600 hover:text-gray-900"
                >
                  <ArrowLeft className="h-4 w-4" />
                  Quay lại khóa học
                </Button>
              </div>
              <h1 className="mb-2 font-bold text-2xl text-gray-900">
                {lesson.name}
              </h1>
              <p className="text-gray-600 text-sm leading-relaxed">
                {lesson.description}
              </p>
            </div>
            <div className="flex items-center gap-4">
              {/* Published Status Toggle */}
              <div className="flex items-center gap-3 rounded-lg border border-gray-200 bg-gray-50 px-4 py-2">
                <Switch
                  checked={lesson.published_at != null}
                  onCheckedChange={setPublished}
                />
                <span
                  className={`font-medium text-sm ${
                    lesson.published_at ? "text-green-700" : "text-gray-600"
                  }`}
                >
                  {lesson.published_at ? "Đã xuất bản" : "Chưa xuất bản"}
                </span>
              </div>
            </div>
          </div>
        </div>

        {loading ? (
          <div className="flex h-96 items-center justify-center rounded-xl border border-gray-200 bg-white">
            <div className="text-center">
              <div className="mx-auto mb-4 h-12 w-12 animate-spin rounded-full border-4 border-gray-200 border-t-blue-600"></div>
              <p className="text-gray-600">Đang tải nội dung bài học...</p>
            </div>
          </div>
        ) : (
          <div className="flex gap-6">
            {/* Sidebar - Section Thumbnails */}
            <div className="w-80 flex-shrink-0">
              <div className="sticky top-6 space-y-4">
                {/* Section Thumbnails */}
                <div className="rounded-xl border border-gray-200 bg-white p-4 shadow-sm">
                  <h3 className="mb-4 font-semibold text-gray-900 text-lg">
                    Các phần
                  </h3>
                  <SectionThumbnail
                    sections={sections}
                    selected={selectedSection}
                    setSelected={setSelectedSection}
                    deleteSection={removeSection}
                    changeIndex={changeIndex}
                    disabled={changingIndex || loadingSection}
                  />
                </div>

                {/* Add Section Button */}
                <div className="rounded-xl border border-gray-200 bg-white p-4 shadow-sm">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button className="w-full" size="lg">
                        <Plus className="mr-2 h-5 w-5" />
                        Thêm phần mới
                        <ChevronDown className="ml-2 h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent className="w-64 rounded-lg">
                      <DropdownMenuItem
                        onSelect={() => addSections("TEXT")}
                        className="flex items-center gap-3 p-3"
                      >
                        <div className="flex h-8 w-8 items-center justify-center rounded bg-blue-100">
                          <FileText className="h-4 w-4 text-blue-600" />
                        </div>
                        <div>
                          <p className="font-medium">Văn bản</p>
                          <p className="text-gray-500 text-xs">
                            Thêm nội dung văn bản rich text
                          </p>
                        </div>
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onSelect={() => addSections("GAME")}
                        className="flex items-center gap-3 p-3"
                      >
                        <div className="flex h-8 w-8 items-center justify-center rounded bg-green-100">
                          <Gamepad2 className="h-4 w-4 text-green-600" />
                        </div>
                        <div>
                          <p className="font-medium">Trò chơi</p>
                          <p className="text-gray-500 text-xs">
                            Tạo mini game tương tác
                          </p>
                        </div>
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onSelect={() => addSections("CHALLENGE")}
                        className="flex items-center gap-3 p-3"
                      >
                        <div className="flex h-8 w-8 items-center justify-center rounded bg-orange-100">
                          <Trophy className="h-4 w-4 text-orange-600" />
                        </div>
                        <div>
                          <p className="font-medium">Thử thách</p>
                          <p className="text-gray-500 text-xs">
                            Câu hỏi kiểm tra kiến thức
                          </p>
                        </div>
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onSelect={() => addSections("AIGENERATED")}
                        className="flex items-center gap-3 p-3"
                      >
                        <div className="flex h-8 w-8 items-center justify-center rounded bg-purple-100">
                          <Sparkles className="h-4 w-4 text-purple-600" />
                        </div>
                        <div>
                          <p className="font-medium">AI Generated</p>
                          <p className="text-gray-500 text-xs">
                            Nội dung được tạo bởi AI
                          </p>
                        </div>
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>
            </div>

            {/* Main Content Area */}
            <div className="w-[73%]">
              <div className="relative rounded-xl border border-gray-200 bg-white shadow-sm">
                {/* Save Status Indicator */}
                {sections?.[selectedSection]?.type === "TEXT" && (
                  <div className="-top-5 absolute right-4 z-10 flex items-center gap-2 rounded-full bg-white px-3 py-1.5 shadow-md">
                    {saving ? (
                      <>
                        <Loader2 className="h-4 w-4 animate-spin text-blue-600" />
                        <span className="font-medium text-blue-600 text-sm">
                          Đang lưu...
                        </span>
                      </>
                    ) : (
                      <>
                        <CheckCircle className="h-4 w-4 text-green-600" />
                        <span className="font-medium text-green-600 text-sm">
                          Đã lưu
                        </span>
                      </>
                    )}
                  </div>
                )}

                <SectionCard
                  selectedSection={selectedSection}
                  setSelectedSection={setSelectedSection}
                  sections={sections}
                  updateSection={setSectionsContent}
                />
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
