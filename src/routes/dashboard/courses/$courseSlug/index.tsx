import { useSuspenseQuery } from "@tanstack/react-query";
import { createFileRoute } from "@tanstack/react-router";
import { BarChart3, BookOpen, History, TrendingUp, Users } from "lucide-react";
import { useState } from "react";
import { CourseContent } from "@/components/course-detail/course-content";
import { CourseHistory } from "@/components/course-detail/course-history";
import { CoursePermissions } from "@/components/course-detail/course-permissions";
import { CourseStats } from "@/components/course-detail/course-stats";
import { CourseViewHeader } from "@/components/course-detail/course-view-header";
import { CourseViewOverview } from "@/components/course-detail/course-view-overview";
import { StudentsTable } from "@/components/course-detail/students-table";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { m } from "@/paraglide/messages.js";
import { courseQueryOptions } from "@/services/courses/index";

export const Route = createFileRoute("/dashboard/courses/$courseSlug/")({
  loader: async ({ params: { courseSlug }, context }) => {
    return { courseSlug };
  },
  component: RouteComponent,
});

function RouteComponent() {
  const { courseSlug } = Route.useLoaderData();
  const { data: course } = useSuspenseQuery(courseQueryOptions(courseSlug));

  const [activeTab, setActiveTab] = useState("overview");

  return (
    <div className="min-h-screen bg-gray-50">
      <CourseViewHeader course={course} />
      <main className="flex-1 px-4 py-6 sm:px-6 lg:px-8">
        <div className="mx-auto max-w-7xl">
          <Tabs
            value={activeTab}
            onValueChange={setActiveTab}
            className="w-full"
          >
            {/* Mobile dropdown for tabs */}
            <div className="mb-6 block sm:hidden">
              <select
                value={activeTab}
                onChange={(e) => setActiveTab(e.target.value)}
                className="w-full rounded-lg border border-gray-300 bg-white px-3 py-2 font-medium text-sm shadow-sm focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary"
              >
                <option value="overview">
                  📈 {m["courseDetail.tabs.overview"]?.() ?? "Tổng quan"}
                </option>
                <option value="stats">
                  📊 {m["courseDetail.tabs.stats"]?.() ?? "Thống kê"}
                </option>
                <option value="content">
                  📚 {m["courseDetail.tabs.content"]?.() ?? "Nội dung"}
                </option>
                <option value="permissions">
                  👥 {m["courseDetail.tabs.permissions"]?.() ?? "Phân quyền"}
                </option>
                <option value="history">
                  🕒 {m["courseDetail.tabs.history"]?.() ?? "Lịch sử"}
                </option>
              </select>
            </div>

            {/* Desktop tabs */}
            <TabsList className="!pb-2 hidden w-full rounded-xl px-1 shadow-sm sm:grid sm:grid-cols-3 lg:grid-cols-5">
              <TabsTrigger
                value="overview"
                className="flex items-center gap-1 text-xs transition-all duration-200 sm:gap-2 sm:text-sm"
              >
                <TrendingUp className="h-3 w-3 sm:h-4 sm:w-4" />
                <span className="hidden sm:inline">
                  {m["courseDetail.tabs.overview"]?.() ?? "Tổng quan"}
                </span>
                <span className="sm:hidden">TQ</span>
              </TabsTrigger>
              <TabsTrigger
                value="stats"
                className="flex items-center gap-1 text-xs transition-all duration-200 sm:gap-2 sm:text-sm"
              >
                <BarChart3 className="h-3 w-3 sm:h-4 sm:w-4" />
                <span className="hidden sm:inline">
                  {m["courseDetail.tabs.stats"]?.() ?? "Thống kê"}
                </span>
                <span className="sm:hidden">TK</span>
              </TabsTrigger>
              <TabsTrigger
                value="content"
                className="flex items-center gap-1 text-xs transition-all duration-200 sm:gap-2 sm:text-sm"
              >
                <BookOpen className="h-3 w-3 sm:h-4 sm:w-4" />
                <span className="hidden sm:inline">
                  {m["courseDetail.tabs.content"]?.() ?? "Nội dung"}
                </span>
                <span className="sm:hidden">ND</span>
              </TabsTrigger>
              <TabsTrigger
                value="permissions"
                className="flex items-center gap-1 text-xs transition-all duration-200 sm:gap-2 sm:text-sm lg:col-span-1"
              >
                <Users className="h-3 w-3 sm:h-4 sm:w-4" />
                <span className="hidden sm:inline">
                  {m["courseDetail.tabs.permissions"]?.() ?? "Phân quyền"}
                </span>
                <span className="sm:hidden">PQ</span>
              </TabsTrigger>
              <TabsTrigger
                value="history"
                className="flex items-center gap-1 text-xs transition-all duration-200 sm:gap-2 sm:text-sm lg:col-span-1"
              >
                <History className="h-3 w-3 sm:h-4 sm:w-4" />
                <span className="hidden sm:inline">
                  {m["courseDetail.tabs.history"]?.() ?? "Lịch sử"}
                </span>
                <span className="sm:hidden">LS</span>
              </TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="mt-4 sm:mt-6">
              <div className="space-y-4 sm:space-y-6">
                <CourseViewOverview course={course} />
              </div>
            </TabsContent>

            <TabsContent
              value="stats"
              className="mt-4 space-y-4 sm:mt-6 sm:space-y-6"
            >
              <CourseStats course={course} />
              <div className="overflow-hidden">
                <StudentsTable />
              </div>
            </TabsContent>

            <TabsContent value="content" className="mt-4 sm:mt-6">
              <div className="space-y-4 sm:space-y-6">
                <CourseContent course={course} />
              </div>
            </TabsContent>

            <TabsContent value="permissions" className="mt-4 sm:mt-6">
              <div className="space-y-4 sm:space-y-6">
                <CoursePermissions />
              </div>
            </TabsContent>

            <TabsContent value="history" className="mt-4 sm:mt-6">
              <div className="space-y-4 sm:space-y-6">
                <CourseHistory history={[]} />
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </main>
    </div>
  );
}
