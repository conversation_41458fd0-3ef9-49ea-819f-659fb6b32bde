/**
 * Department represents a company department with its users and permissions
 */

export enum PERMISSION {
  READ_USER = "read_user",
  MANAGE_USER = "manage_user",
  READ_COURSE = "read_course",
  MANAGE_COURSE = "manage_course",
  MANAGE_GROUP = "manage_group",
  REPORT = "report",
}

export interface PermissionInfo {
  id: string;
  name: string;
  description: string;
}

export const PERMISSION_INFO: Record<PERMISSION, PermissionInfo> = {
  [PERMISSION.READ_USER]: {
    id: PERMISSION.READ_USER,
    name: "Xem người dùng",
    description: "<PERSON><PERSON> thể xem danh sách và thông tin người dùng",
  },
  [PERMISSION.MANAGE_USER]: {
    id: PERMISSION.MANAGE_USER,
    name: "Quản lý người dùng",
    description: "<PERSON><PERSON> thể thêm, sửa, xóa người dùng",
  },
  [PERMISSION.READ_COURSE]: {
    id: PERMISSION.READ_COURSE,
    name: "<PERSON>em khóa học",
    description: "<PERSON><PERSON> thể xem danh sách và nội dung khóa học",
  },
  [PERMISSION.MANAGE_COURSE]: {
    id: PERMISSION.MANAGE_COURSE,
    name: "Quản lý khóa học",
    description: "Có thể tạo, sửa, xóa khóa học",
  },
  [PERMISSION.MANAGE_GROUP]: {
    id: PERMISSION.MANAGE_GROUP,
    name: "Quản lý phòng ban",
    description: "Có thể tạo, sửa, xóa phòng ban và phân quyền",
  },
  [PERMISSION.REPORT]: {
    id: PERMISSION.REPORT,
    name: "Xem báo cáo",
    description: "Có thể xem các báo cáo và thống kê",
  },
};

export enum COURSE_PERMISSION {
  READ = "read",
  WRITE = "write",
  REPORT = "report",
}

export interface Department {
  id: number;
  name: string;
  users: number;
  description: string;
  permissions: PERMISSION[];
}

/**
 * Brand color configuration for the organization
 */
export interface BrandColors {
  primary: string;
  secondary: string;
  accent: string;
}

/**
 * New department form data
 */
export interface NewDepartment {
  name: string;
  permissions: string[];
}
