import {
  IconCamera,
  IconChartBar,
  IconDashboard,
  IconDatabase,
  IconFileAi,
  IconFileDescription,
  IconFileWord,
  IconFolder,
  IconHelp,
  IconInnerShadowTop,
  IconListDetails,
  IconReport,
  IconSearch,
  IconSettings,
  IconUsers,
} from "@tabler/icons-react";
import * as React from "react";
import logoUrl from "@/assets/images/aicademy-logo-mobile.png";

import { NavDocuments } from "@/components/nav-documents";
import { NavMain } from "@/components/nav-main";
import { NavSecondary } from "@/components/nav-secondary";
import { NavUser } from "@/components/nav-user";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
} from "@/components/ui/sidebar";
import { m } from "@/paraglide/messages.js";
import { LanguageToggle } from "./LanguageToggle";
import { Separator } from "./ui/separator";

const data = {
  user: {
    name: "shadcn",
    email: "<EMAIL>",
    avatar: "",
  },
  navMain: [
    {
      title: m["sidebar.organizationSettings"](),
      url: "/dashboard/organization",
      icon: IconDashboard,
    },
    {
      title: m["sidebar.userManagement"](),
      url: "/dashboard/users",
      icon: IconListDetails,
    },
    {
      title: m["sidebar.courseManagement"](),
      url: "/dashboard/courses",
      icon: IconChartBar,
    },
    {
      title: m["sidebar.wikiManagement"](),
      url: "/dashboard/wiki",
      icon: IconChartBar,
    },
    {
      title: m["sidebar.analyticsReports"](),
      url: "/dashboard/analytics",
      icon: IconFolder,
    },
    // {
    //   title: m["sidebar.collaborationLXP"](),
    //   url: "#",
    //   icon: IconUsers,
    // },
  ],
  navClouds: [
    {
      title: "Capture",
      icon: IconCamera,
      isActive: true,
      url: "#",
      items: [
        {
          title: "Active Proposals",
          url: "#",
        },
        {
          title: "Archived",
          url: "#",
        },
      ],
    },
    {
      title: "Proposal",
      icon: IconFileDescription,
      url: "#",
      items: [
        {
          title: "Active Proposals",
          url: "#",
        },
        {
          title: "Archived",
          url: "#",
        },
      ],
    },
    {
      title: "Prompts",
      icon: IconFileAi,
      url: "#",
      items: [
        {
          title: "Active Proposals",
          url: "#",
        },
        {
          title: "Archived",
          url: "#",
        },
      ],
    },
  ],
  navSecondary: [
    {
      title: "Settings",
      url: "#",
      icon: IconSettings,
    },
    {
      title: "Get Help",
      url: "#",
      icon: IconHelp,
    },
    {
      title: "Search",
      url: "#",
      icon: IconSearch,
    },
  ],
  documents: [
    {
      name: "Data Library",
      url: "#",
      icon: IconDatabase,
    },
    {
      name: "Reports",
      url: "#",
      icon: IconReport,
    },
    {
      name: "Word Assistant",
      url: "#",
      icon: IconFileWord,
    },
  ],
};

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  return (
    <Sidebar collapsible="offcanvas" {...props}>
      <SidebarHeader>
        <div className="flex items-center gap-2">
          <img src={logoUrl} alt="" className="size-8" />
          <div>
            <p className="font-semibold text-sm">Aicademy Admin</p>
            <p className="font-medium text-muted-foreground text-xs">
              Learning platform
            </p>
          </div>
          <LanguageToggle />
        </div>
        <Separator />
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={data.navMain} />
        {/* <NavDocuments items={data.documents} /> */}
        <NavSecondary items={data.navSecondary} className="mt-auto" />
      </SidebarContent>
      <SidebarFooter>
        <NavUser />
      </SidebarFooter>
    </Sidebar>
  );
}
