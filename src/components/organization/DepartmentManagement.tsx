import { Edit, Loader2, Shield, Trash2 } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { deleteGroup, useGroups } from "@/services/admin";
import { Department } from "../../types/organization";
import { DepartmentDialog } from "./DepartmentDialog";
import { PermissionRenderer } from "./PermissionRenderer";

/**
 * Component for managing departments and their permissions
 * Displays departments table with add/edit/delete functionality
 */
export function DepartmentManagement() {
  // Query departments from API
  const { data: departments = [], isLoading, error, refetch } = useGroups();

  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [selectedDepartmentId, setSelectedDepartmentId] = useState<
    number | null
  >(null);
  const [isDeleting, setIsDeleting] = useState(false);

  /**
   * Selected department for editing
   */
  const [selectedEditDepartment, setSelectedEditDepartment] = useState<
    Department | undefined
  >(undefined);

  /**
   * Handle department edit action
   */
  const handleEditDepartment = (department: Department) => {
    setSelectedEditDepartment(department);
  };

  /**
   * Handle department delete action
   */
  const handleDeleteDepartment = async (departmentId: number) => {
    try {
      setIsDeleting(true);
      await deleteGroup(departmentId);
      toast.success("Xóa phòng ban thành công");
      // Refresh the departments list
      refetch();
    } catch (error) {
      console.error("Error deleting department:", error);
      toast.error("Không thể xóa phòng ban", {
        description: "Đã có lỗi xảy ra. Vui lòng thử lại sau.",
      });
    } finally {
      setIsDeleting(false);
      setDeleteConfirmOpen(false);
      setSelectedDepartmentId(null);
    }
  };

  /**
   * Open delete confirmation dialog
   */
  const confirmDelete = (departmentId: number) => {
    setSelectedDepartmentId(departmentId);
    setDeleteConfirmOpen(true);
  };

  // Show error message if API call failed
  if (error) {
    toast.error("Không thể tải danh sách phòng ban", {
      description: "Đã có lỗi xảy ra khi tải dữ liệu. Vui lòng thử lại sau.",
    });
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              Quản lý phòng ban và phân quyền
            </CardTitle>
            <CardDescription>
              Tạo phòng ban và phân quyền truy cập cho từng bộ phận
            </CardDescription>
          </div>
          <DepartmentDialog onSuccess={() => refetch()} />
        </div>
      </CardHeader>

      <CardContent>
        {isLoading ? (
          <div className="flex items-center justify-center py-10">
            <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
            <span className="ml-2 text-muted-foreground">
              Đang tải dữ liệu...
            </span>
          </div>
        ) : departments?.length === 0 ? (
          <div className="py-10 text-center">
            <p className="text-muted-foreground">
              Chưa có phòng ban nào được tạo
            </p>
            <p className="mt-1 text-muted-foreground text-sm">
              Nhấn nút "Thêm phòng ban" để bắt đầu
            </p>
          </div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Phòng ban</TableHead>
                <TableHead>Số người dùng</TableHead>
                <TableHead>Quyền truy cập</TableHead>
                <TableHead className="text-right">Thao tác</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {departments.map((dept) => (
                <TableRow key={dept.id}>
                  <TableCell className="font-medium">{dept.name}</TableCell>
                  <TableCell>{dept.users} người</TableCell>
                  <TableCell>
                    {dept?.permissions?.length ? (
                      <PermissionRenderer permissions={dept.permissions} />
                    ) : (
                      <span className="text-muted-foreground text-sm">
                        Không có quyền nào
                      </span>
                    )}
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end gap-2">
                      <DepartmentDialog
                        department={dept}
                        onSuccess={() => refetch()}
                        trigger={
                          <Button variant="ghost" size="sm">
                            <Edit className="h-4 w-4" />
                          </Button>
                        }
                      />
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-destructive"
                        onClick={() => confirmDelete(dept.id)}
                        disabled={
                          isDeleting && selectedDepartmentId === dept.id
                        }
                      >
                        {isDeleting && selectedDepartmentId === dept.id ? (
                          <Loader2 className="h-4 w-4 animate-spin" />
                        ) : (
                          <Trash2 className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </CardContent>

      {/* Delete confirmation dialog */}
      <AlertDialog open={deleteConfirmOpen} onOpenChange={setDeleteConfirmOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Xác nhận xóa phòng ban</AlertDialogTitle>
            <AlertDialogDescription>
              Bạn có chắc chắn muốn xóa phòng ban này? Hành động này không thể
              hoàn tác.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isDeleting}>Hủy</AlertDialogCancel>
            <AlertDialogAction
              onClick={() =>
                selectedDepartmentId &&
                handleDeleteDepartment(selectedDepartmentId)
              }
              className="bg-red-600 hover:bg-red-700 focus:ring-red-600"
              disabled={isDeleting}
            >
              {isDeleting ? "Đang xóa..." : "Xóa phòng ban"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Card>
  );
}
