import { PencilIcon, Plus } from "lucide-react";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON>itle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { createGroup, updateGroup, useGroups } from "@/services/admin";
import {
  Department,
  NewDepartment,
  PERMISSION,
  PERMISSION_INFO,
} from "../../types/organization";

interface DepartmentDialogProps {
  onSuccess?: () => void;
  department?: Department; // If provided, we're in edit mode
  trigger?: React.ReactNode; // Custom trigger element
}

/**
 * Dialog component for adding or editing departments with permission assignment
 * Includes validation for unique department names
 */
export function DepartmentDialog({
  onSuccess,
  department,
  trigger,
}: DepartmentDialogProps) {
  const isEditMode = !!department;
  const [isOpen, setIsOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState<
    NewDepartment & { description: string; id?: number }
  >({
    name: "",
    description: "",
    permissions: [],
    id: undefined,
  });

  // Initialize form with department data when in edit mode
  useEffect(() => {
    if (department && isEditMode) {
      setFormData({
        id: department.id,
        name: department.name,
        description: department.description || "",
        permissions: department.permissions || [], // Ensure permissions is always an array
      });
    }
  }, [department, isEditMode]);

  // Get departments list from API
  const { data: departments = [] } = useGroups();

  /**
   * Check if department name is unique
   */
  const isDepartmentNameUnique = (name: string) => {
    return !departments.some(
      (dept) =>
        dept.id !== formData.id &&
        dept.name.toLowerCase() === name.toLowerCase(),
    );
  };

  /**
   * Handle permission toggle for department
   */
  const handlePermissionToggle = (permissionId: string, checked: boolean) => {
    // Ensure permissions is always an array
    const currentPermissions = formData.permissions || [];

    if (checked) {
      setFormData({
        ...formData,
        permissions: [...currentPermissions, permissionId],
      });
    } else {
      setFormData({
        ...formData,
        permissions: currentPermissions.filter((p) => p !== permissionId),
      });
    }
  };

  /**
   * Handle form submission
   */
  const handleSubmit = async () => {
    if (
      formData.name &&
      (!isEditMode || isDepartmentNameUnique(formData.name))
    ) {
      try {
        setLoading(true);
        // Ensure permissions is always an array and convert to PERMISSION enum values
        const currentPermissions = formData.permissions || [];
        const enumPermissions = currentPermissions.map(
          (p) => p as unknown as PERMISSION,
        );

        if (isEditMode && formData.id) {
          // Call API to update group
          await updateGroup({
            id: formData.id,
            description: formData.description,
            permissions: enumPermissions,
          });

          // Show success message
          toast.success("Cập nhật phòng ban thành công!", {
            description: `Phòng ban "${formData.name}" đã được cập nhật với ${
              (formData.permissions || []).length
            } quyền.`,
          });
        } else {
          // Call API to create new group
          await createGroup({
            name: formData.name,
            description: formData.description,
            permissions: enumPermissions,
          });

          // Show success message
          toast.success("Tạo phòng ban mới thành công!", {
            description: `Phòng ban "${formData.name}" đã được tạo với ${
              (formData.permissions || []).length
            } quyền.`,
          });
        }

        // Notify parent component to refresh the list
        onSuccess?.();

        // Reset form and close dialog
        resetForm();
        setIsOpen(false);
      } catch (error) {
        console.error(
          `Failed to ${isEditMode ? "update" : "create"} department:`,
          error,
        );
        // Show error message
        toast.error(`Không thể ${isEditMode ? "cập nhật" : "tạo"} phòng ban`, {
          description: `Đã có lỗi xảy ra khi ${
            isEditMode ? "cập nhật" : "tạo"
          } phòng ban. Vui lòng thử lại sau.`,
        });
      } finally {
        setLoading(false);
      }
    }
  };

  /**
   * Reset form to initial state
   */
  const resetForm = () => {
    setFormData({
      name: "",
      description: "",
      permissions: [],
      id: undefined,
    });
  };

  /**
   * Handle dialog close - reset form
   */
  const handleClose = () => {
    resetForm();
    setIsOpen(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button variant="default" className="cursor-pointer bg-primary">
            <Plus className="mr-2 h-4 w-4" />
            {isEditMode ? "Sửa phòng ban" : "Thêm phòng ban"}
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="max-w-lg">
        <DialogHeader>
          <DialogTitle>
            {isEditMode ? "Sửa phòng ban" : "Thêm phòng ban mới"}
          </DialogTitle>
          <DialogDescription>
            {isEditMode
              ? "Cập nhật thông tin phòng ban và phân quyền truy cập"
              : "Tạo phòng ban mới và phân quyền truy cập"}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Department name input */}
          <div className="space-y-2">
            <Label htmlFor="dept-name">Tên phòng ban</Label>
            <Input
              id="dept-name"
              value={formData.name}
              onChange={(e) =>
                setFormData({ ...formData, name: e.target.value })
              }
              placeholder="Nhập tên phòng ban"
              disabled={isEditMode} // Cannot edit name in edit mode
            />
            {formData.name &&
              !isEditMode &&
              !isDepartmentNameUnique(formData.name) && (
                <p className="text-red-500 text-sm">Tên phòng ban đã tồn tại</p>
              )}
          </div>

          {/* Department description input */}
          <div className="space-y-2">
            <Label htmlFor="dept-description">Mô tả</Label>
            <Textarea
              id="dept-description"
              value={formData.description}
              onChange={(e) =>
                setFormData({
                  ...formData,
                  description: e.target.value,
                })
              }
              placeholder="Nhập mô tả phòng ban"
              rows={3}
            />
          </div>

          {/* Permissions selection */}
          <div className="space-y-2">
            <Label>Phân quyền</Label>
            <div className="max-h-48 space-y-2 overflow-y-auto">
              {Object.entries(PERMISSION_INFO).map(([id, permission]) => (
                <div key={id} className="flex items-center space-x-2">
                  <Input
                    type="checkbox"
                    id={id}
                    className="mt-1 w-max"
                    checked={(formData.permissions || []).includes(id)}
                    onChange={(e) =>
                      handlePermissionToggle(id, e.target.checked)
                    }
                    size={12}
                  />
                  <div className="flex-1">
                    <Label htmlFor={id} className="font-medium">
                      {permission.name}
                    </Label>
                    <p className="text-muted-foreground text-xs">
                      {permission.description}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose}>
            Hủy
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={
              loading ||
              !formData.name ||
              !formData.description ||
              (!isEditMode && !isDepartmentNameUnique(formData.name))
            }
          >
            {loading
              ? "Đang xử lý..."
              : isEditMode
                ? "Cập nhật"
                : "Tạo phòng ban"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
