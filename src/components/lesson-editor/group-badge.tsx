import { Plus, Tag } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface GroupBadgeProps {
  groupName?: string | null;
  isUngrouped?: boolean;
  onAddToGroup?: () => void;
  className?: string;
}

export const GroupBadge = ({
  groupName,
  isUngrouped = false,
  onAddToGroup,
  className,
}: GroupBadgeProps) => {
  if (!groupName || isUngrouped) {
    return (
      <div className={cn("flex items-center gap-1", className)}>
        <Badge variant="outline" className="border-gray-300 text-gray-500">
          <Tag className="mr-1 h-3 w-3" />
          Không nhóm
        </Badge>
        {onAddToGroup && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onAddToGroup}
            className="h-6 px-2 text-blue-600 text-xs hover:bg-blue-50 hover:text-blue-700"
          >
            <Plus className="mr-1 h-3 w-3" />
            Thêm vào nhóm
          </Button>
        )}
      </div>
    );
  }

  return (
    <div className={cn("flex items-center gap-1", className)}>
      <Badge
        variant="secondary"
        className="border-blue-200 bg-blue-50 text-blue-700"
      >
        <Tag className="mr-1 h-3 w-3" />
        {groupName}
      </Badge>
      {onAddToGroup && (
        <Button
          variant="ghost"
          size="sm"
          onClick={onAddToGroup}
          className="h-6 px-2 text-blue-600 text-xs hover:bg-blue-50 hover:text-blue-700"
        >
          <Plus className="mr-1 h-3 w-3" />
          Đổi nhóm
        </Button>
      )}
    </div>
  );
};
