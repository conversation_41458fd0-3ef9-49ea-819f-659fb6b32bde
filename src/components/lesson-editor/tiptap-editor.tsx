import RichTextEditor, { <PERSON><PERSON><PERSON>, Editor } from "aicademy-react-tiptap-editor";
import { Attachment } from "aicademy-react-tiptap-editor/attachment";
import { Blockquote } from "aicademy-react-tiptap-editor/blockquote";
import { Bold } from "aicademy-react-tiptap-editor/bold";
import { BulletList } from "aicademy-react-tiptap-editor/bulletlist";
import { Clear } from "aicademy-react-tiptap-editor/clear";
import { Code } from "aicademy-react-tiptap-editor/code";
import { CodeBlock } from "aicademy-react-tiptap-editor/codeblock";
import { Color } from "aicademy-react-tiptap-editor/color";
import { Emoji } from "aicademy-react-tiptap-editor/emoji";
import { ExportPdf } from "aicademy-react-tiptap-editor/exportpdf";
import { ExportWord } from "aicademy-react-tiptap-editor/exportword";
import { FontFamily } from "aicademy-react-tiptap-editor/fontfamily";
import { FontSize } from "aicademy-react-tiptap-editor/fontsize";
import { FormatPainter } from "aicademy-react-tiptap-editor/formatpainter";
import { HighlightBox } from "aicademy-react-tiptap-editor/HighlightBox.js";
import { Heading } from "aicademy-react-tiptap-editor/heading";
import { Highlight } from "aicademy-react-tiptap-editor/highlight";
import { History } from "aicademy-react-tiptap-editor/history";
import { HorizontalRule } from "aicademy-react-tiptap-editor/horizontalrule";
import { Iframe } from "aicademy-react-tiptap-editor/iframe";
import { Image } from "aicademy-react-tiptap-editor/image";
import { ImageGif } from "aicademy-react-tiptap-editor/imagegif";
import { ImportWord } from "aicademy-react-tiptap-editor/importword";
import { Indent } from "aicademy-react-tiptap-editor/indent";
import { Italic } from "aicademy-react-tiptap-editor/italic";
import { LineHeight } from "aicademy-react-tiptap-editor/lineheight";
import { Link } from "aicademy-react-tiptap-editor/link";
import { locale } from "aicademy-react-tiptap-editor/locale-bundle";
import { Mention } from "aicademy-react-tiptap-editor/mention";
import { MoreMark } from "aicademy-react-tiptap-editor/moremark";
import { ColumnActionButton } from "aicademy-react-tiptap-editor/multicolumn";
import { OrderedList } from "aicademy-react-tiptap-editor/orderedlist";
import { SearchAndReplace } from "aicademy-react-tiptap-editor/searchandreplace";
import { SlashCommand } from "aicademy-react-tiptap-editor/slashcommand";
import { Strike } from "aicademy-react-tiptap-editor/strike";
import { Table } from "aicademy-react-tiptap-editor/table";
import { TableOfContents } from "aicademy-react-tiptap-editor/tableofcontent";
import { TaskList } from "aicademy-react-tiptap-editor/tasklist";
import { TextAlign } from "aicademy-react-tiptap-editor/textalign";
import { TextDirection } from "aicademy-react-tiptap-editor/textdirection";
import { TextUnderline } from "aicademy-react-tiptap-editor/textunderline";
import { Video } from "aicademy-react-tiptap-editor/video";
import { useCallback, useEffect, useRef, useState } from "react";
import "aicademy-react-tiptap-editor/lib/style.css";
function convertBase64ToBlob(base64: string) {
  const arr = base64.split(",");
  const mime = arr[0].match(/:(.*?);/)![1];
  const bstr = atob(arr[1]);
  let n = bstr.length;
  const u8arr = new Uint8Array(n);
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }
  return new Blob([u8arr], { type: mime });
}

const extensions = [
  BaseKit.configure({
    placeholder: {
      showOnlyCurrent: true,
    },
    characterCount: {
      limit: 50_000,
    },
  }),
  History,
  SearchAndReplace,
  TableOfContents,
  FormatPainter.configure({ spacer: true }),
  Clear,
  FontFamily,
  Heading.configure({ spacer: true }),
  FontSize,
  Bold,
  Italic,
  TextUnderline,
  Strike,
  MoreMark,
  Emoji,
  Color.configure({ spacer: true }),
  Highlight,
  BulletList,
  OrderedList,
  TextAlign.configure({ types: ["heading", "paragraph"], spacer: true }),
  Indent,
  LineHeight,
  TaskList.configure({
    spacer: true,
    taskItem: {
      nested: true,
    },
  }),
  Link,
  Image.configure({
    upload: (files: File) => {
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve(URL.createObjectURL(files));
        }, 500);
      });
    },
  }),
  Video.configure({
    upload: (files: File) => {
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve(URL.createObjectURL(files));
        }, 500);
      });
    },
  }),
  ImageGif.configure({
    GIPHY_API_KEY: import.meta.env.VITE_GIPHY_API_KEY as string,
  }),
  Blockquote,
  SlashCommand,
  HorizontalRule,
  Code.configure({
    toolbar: false,
  }),
  CodeBlock,
  ColumnActionButton,
  Table,
  Iframe,
  ExportPdf.configure({ spacer: true }),
  ImportWord.configure({
    upload: (files: File[]) => {
      const f = files.map((file) => ({
        src: URL.createObjectURL(file),
        alt: file.name,
      }));
      return Promise.resolve(f);
    },
  }),
  ExportWord,
  TextDirection,
  Mention,
  Attachment.configure({
    upload: (file: File) => {
      // fake upload return base 64
      const reader = new FileReader();
      reader.readAsDataURL(file);

      return new Promise<string>((resolve) => {
        setTimeout(() => {
          const blob = convertBase64ToBlob(reader.result as string);
          resolve(URL.createObjectURL(blob));
        }, 300);
      });
    },
  }),
  HighlightBox.configure({ spacer: true }),
];

// eslint-disable-next-line @typescript-eslint/no-explicit-any
function debounce<T extends (...args: unknown[]) => unknown>(
  func: T,
  wait: number,
) {
  let timeout: NodeJS.Timeout;
  return function (...args: Parameters<T>) {
    clearTimeout(timeout);
    timeout = setTimeout(() => func.apply(this, args), wait);
  };
}

interface BeautyEditorProps {
  initialContent?: string;
  content: string;
  setContent: (content: string) => void;
}

function BeautyEditor({
  initialContent,
  content,
  setContent,
}: BeautyEditorProps) {
  const editorRef = useRef<{ editor: Editor | null }>(null);
  const [theme, setTheme] = useState("light");
  const [disable, setDisable] = useState(false);

  const onValueChange = useCallback(
    debounce((value: string | object) => {
      // Ensure we always pass a JSON string to setContent
      try {
        setContent(value as string);
      } catch (error) {
        console.error("Error converting content to JSON:", error);
        setContent(JSON.stringify({ type: "doc", content: [] }));
      }
    }, 500),
    [],
  );

  // Parse content safely
  let parsedContent: Record<string, unknown>;
  try {
    parsedContent = typeof content === "string" ? JSON.parse(content) : content;
  } catch (error) {
    console.error("Error parsing content:", error);
    parsedContent = { type: "doc", content: [] };
  }

  useEffect(() => {
    if (!initialContent || !editorRef.current?.editor) return;
    const initialContentParsed = JSON.parse(initialContent);
    editorRef.current.editor.commands.setContent(initialContentParsed);
  }, [initialContent]);

  return (
    <div className="w-full">
      <RichTextEditor
        ref={editorRef}
        output="json"
        content={parsedContent as unknown as string}
        onChangeContent={onValueChange}
        extensions={extensions}
        dark={theme === "dark"}
        disabled={disable}
      />
    </div>
  );
}

export default BeautyEditor;
