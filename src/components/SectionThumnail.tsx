import {
  <PERSON><PERSON><PERSON><PERSON>,
  Gamepad2,
  GripVertical,
  Loader2,
  <PERSON><PERSON>les,
  Trash2,
  Trophy,
} from "lucide-react";
import React, { useEffect, useRef } from "react";
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from "@dnd-kit/core";
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
  useSortable,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { restrictToVerticalAxis } from "@dnd-kit/modifiers";
import { Button } from "@/components/ui/button";
import { Section } from "@/types/lessons";

interface ThumbnailProps {
  sectionId: string;
}

interface SectionThumbnailProps {
  sections: Section[];
  selected: number;
  setSelected: (index: number) => void;
  deleteSection: (index: number) => void;
  onReorder: (oldIndex: number, newIndex: number) => void;
  disabled: boolean;
}

function Thumbnail({ sectionId }: ThumbnailProps) {
  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const waitRender = setInterval(() => {
      const originalElement = document.getElementById(
        `section-view-${sectionId}`
      );
      if (originalElement) {
        const cloned = originalElement.cloneNode(true) as HTMLElement;
        cloned.id = `section-thumbnail-${sectionId}`;
        cloned.style.transform = "scale(0.3)";
        cloned.style.transformOrigin = "top left";
        cloned.style.width = "calc(100% / 0.3)";
        cloned.style.height = "auto";
        cloned.style.pointerEvents = "none";
        if (ref.current?.childNodes[0]) {
          ref.current.replaceChild(cloned, ref.current.childNodes[0]);
          clearInterval(waitRender);
        }
      }
    }, 1000);

    return () => clearInterval(waitRender);
  }, [sectionId]);

  return (
    <div className="h-full w-full overflow-hidden p-1" ref={ref}>
      <div className="flex h-full w-full items-center justify-center">
        ...Loading...
      </div>
    </div>
  );
}

interface SortableItemProps {
  section: Section;
  index: number;
  selected: number;
  setSelected: (index: number) => void;
  deleteSection: (index: number) => void;
  disabled: boolean;
}

function SortableItem({
  section,
  index,
  selected,
  setSelected,
  deleteSection,
  disabled,
}: SortableItemProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: section.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`group relative ${isDragging ? "z-50 opacity-50" : ""}`}
    >
      {/* Drag Handle */}
      <div
        {...attributes}
        {...listeners}
        className="absolute top-2 left-2 z-20 flex h-6 w-6 cursor-grab items-center justify-center rounded-full bg-white/90 shadow-sm backdrop-blur-sm opacity-0 transition-opacity group-hover:opacity-100 active:cursor-grabbing"
        title="Kéo để sắp xếp lại"
      >
        <GripVertical className="h-3 w-3 text-gray-600" />
      </div>

      {/* Action Buttons */}
      <div className="absolute top-2 right-2 z-10 flex flex-col gap-1 opacity-0 transition-opacity group-hover:opacity-100">
        <Button
          type="button"
          variant="outline"
          size="sm"
          disabled={disabled}
          className="h-7 w-7 border-red-200 bg-red-50 p-0 text-red-600 hover:bg-red-100 hover:text-red-700"
          onClick={() => deleteSection(index)}
          title="Xóa phần"
        >
          <Trash2 className="h-3 w-3" />
        </Button>
      </div>

      {/* Section Type Badge */}
      <div className="absolute right-2 bottom-2 z-10 rounded-md bg-white/90 px-2 py-1 shadow-sm backdrop-blur-sm">
        <div className="flex items-center gap-1" title={section.type}>
          {section.type === "TEXT" && (
            <>
              <FileText className="h-3 w-3 text-blue-600" />
              <span className="font-medium text-blue-600 text-xs">Text</span>
            </>
          )}
          {section.type === "GAME" && (
            <>
              <Gamepad2 className="h-3 w-3 text-green-600" />
              <span className="font-medium text-green-600 text-xs">Game</span>
            </>
          )}
          {section.type === "CHALLENGE" && (
            <>
              <Trophy className="h-3 w-3 text-orange-600" />
              <span className="font-medium text-orange-600 text-xs">Quiz</span>
            </>
          )}
          {section.type === "AIGENERATED" && (
            <>
              <Sparkles className="h-3 w-3 text-purple-600" />
              <span className="font-medium text-purple-600 text-xs">AI</span>
            </>
          )}
        </div>
      </div>

      {/* Section Number */}
      <div className="absolute top-2 left-8 z-10 flex h-6 w-6 items-center justify-center rounded-full bg-white/90 shadow-sm backdrop-blur-sm">
        <span className="font-semibold text-gray-700 text-xs">{index + 1}</span>
      </div>

      {/* Thumbnail Container */}
      <div
        onClick={() => setSelected(index)}
        className={`relative h-32 cursor-pointer overflow-hidden rounded-lg border-2 transition-all duration-200 ${
          selected === index
            ? "border-blue-500 bg-blue-50 shadow-md"
            : "border-gray-200 bg-white hover:border-blue-300 hover:shadow-sm"
        }`}
      >
        <Thumbnail sectionId={section.id} />
      </div>
    </div>
  );
}

export default function SectionThumbnail({
  sections,
  selected,
  setSelected,
  deleteSection,
  onReorder,
  disabled,
}: SectionThumbnailProps) {
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (over && active.id !== over.id) {
      const oldIndex = sections.findIndex(
        (section) => section.id === active.id
      );
      const newIndex = sections.findIndex((section) => section.id === over.id);

      if (oldIndex !== -1 && newIndex !== -1) {
        onReorder(oldIndex, newIndex);
      }
    }
  };

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={closestCenter}
      onDragEnd={handleDragEnd}
      modifiers={[restrictToVerticalAxis]}
    >
      <SortableContext
        items={sections.map((section) => section.id)}
        strategy={verticalListSortingStrategy}
      >
        <div className="space-y-3">
          {sections &&
            sections.length > 0 &&
            sections.map((section, index) => (
              <SortableItem
                key={section.id}
                section={section}
                index={index}
                selected={selected}
                setSelected={setSelected}
                deleteSection={deleteSection}
                disabled={disabled}
              />
            ))}
        </div>
      </SortableContext>
    </DndContext>
  );
}
