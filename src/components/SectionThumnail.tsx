import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>2,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>rash2,
  Trophy,
} from "lucide-react";
import React, { useEffect, useRef } from "react";
import { Button } from "@/components/ui/button";
import { Section } from "@/types/lessons";

interface ThumbnailProps {
  sectionId: string;
}

interface SectionThumbnailProps {
  sections: Section[];
  selected: number;
  setSelected: (index: number) => void;
  deleteSection: (index: number) => void;
  changeIndex: (index: number, direction: number) => void;
  disabled: boolean;
}

function Thumbnail({ sectionId }: ThumbnailProps) {
  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const waitRender = setInterval(() => {
      const originalElement = document.getElementById(
        `section-view-${sectionId}`,
      );
      if (originalElement) {
        const cloned = originalElement.cloneNode(true) as HTMLElement;
        cloned.id = `section-thumbnail-${sectionId}`;
        cloned.style.transform = "scale(0.3)";
        cloned.style.transformOrigin = "top left";
        cloned.style.width = "calc(100% / 0.3)";
        cloned.style.height = "auto";
        cloned.style.pointerEvents = "none";
        if (ref.current?.childNodes[0]) {
          ref.current.replaceChild(cloned, ref.current.childNodes[0]);
          clearInterval(waitRender);
        }
      }
    }, 1000);

    return () => clearInterval(waitRender);
  }, [sectionId]);

  return (
    <div className="h-full w-full overflow-hidden p-1" ref={ref}>
      <div className="flex h-full w-full items-center justify-center">
        ...Loading...
      </div>
    </div>
  );
}

export default function SectionThumbnail({
  sections,
  selected,
  setSelected,
  deleteSection,
  changeIndex,
  disabled,
}: SectionThumbnailProps) {
  return (
    <div className="space-y-3">
      {sections &&
        sections.length > 0 &&
        sections.map((section, index) => {
          return (
            <div key={section.id} className="group relative">
              {/* Action Buttons */}
              <div className="absolute top-2 right-2 z-10 flex flex-col gap-1 opacity-0 transition-opacity group-hover:opacity-100">
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  disabled={disabled}
                  className="h-7 w-7 border-red-200 bg-red-50 p-0 text-red-600 hover:bg-red-100 hover:text-red-700"
                  onClick={() => deleteSection(index)}
                  title="Xóa phần"
                >
                  <Trash2 className="h-3 w-3" />
                </Button>

                {index > 0 && (
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    disabled={disabled}
                    className="h-7 w-7 border-gray-200 bg-white p-0 hover:bg-gray-50"
                    title="Di chuyển lên"
                    onClick={() => changeIndex(index, -1)}
                  >
                    {disabled ? (
                      <Loader2 className="h-3 w-3 animate-spin" />
                    ) : (
                      <ChevronUp className="h-3 w-3" />
                    )}
                  </Button>
                )}

                {index < sections.length - 1 && (
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    disabled={disabled}
                    className="h-7 w-7 border-gray-200 bg-white p-0 hover:bg-gray-50"
                    title="Di chuyển xuống"
                    onClick={() => changeIndex(index, 1)}
                  >
                    {disabled ? (
                      <Loader2 className="h-3 w-3 animate-spin" />
                    ) : (
                      <ChevronDown className="h-3 w-3" />
                    )}
                  </Button>
                )}
              </div>

              {/* Section Type Badge */}
              <div className="absolute right-2 bottom-2 z-10 rounded-md bg-white/90 px-2 py-1 shadow-sm backdrop-blur-sm">
                <div className="flex items-center gap-1" title={section.type}>
                  {section.type === "TEXT" && (
                    <>
                      <FileText className="h-3 w-3 text-blue-600" />
                      <span className="font-medium text-blue-600 text-xs">
                        Text
                      </span>
                    </>
                  )}
                  {section.type === "GAME" && (
                    <>
                      <Gamepad2 className="h-3 w-3 text-green-600" />
                      <span className="font-medium text-green-600 text-xs">
                        Game
                      </span>
                    </>
                  )}
                  {section.type === "CHALLENGE" && (
                    <>
                      <Trophy className="h-3 w-3 text-orange-600" />
                      <span className="font-medium text-orange-600 text-xs">
                        Quiz
                      </span>
                    </>
                  )}
                  {section.type === "AIGENERATED" && (
                    <>
                      <Sparkles className="h-3 w-3 text-purple-600" />
                      <span className="font-medium text-purple-600 text-xs">
                        AI
                      </span>
                    </>
                  )}
                </div>
              </div>

              {/* Section Number */}
              <div className="absolute top-2 left-2 z-10 flex h-6 w-6 items-center justify-center rounded-full bg-white/90 shadow-sm backdrop-blur-sm">
                <span className="font-semibold text-gray-700 text-xs">
                  {index + 1}
                </span>
              </div>

              {/* Thumbnail Container */}
              <div
                onClick={() => setSelected(index)}
                className={`relative h-32 cursor-pointer overflow-hidden rounded-lg border-2 transition-all duration-200 ${
                  selected === index
                    ? "border-blue-500 bg-blue-50 shadow-md"
                    : "border-gray-200 bg-white hover:border-blue-300 hover:shadow-sm"
                }`}
              >
                <Thumbnail sectionId={section.id} />
              </div>
            </div>
          );
        })}
    </div>
  );
}
