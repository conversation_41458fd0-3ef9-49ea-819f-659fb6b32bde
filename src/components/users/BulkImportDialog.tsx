import { useQueryClient } from "@tanstack/react-query";
import { <PERSON><PERSON><PERSON> } from "buffer";
import { parse } from "csv-parse/browser/esm/sync";
import {
  AlertCircle,
  CheckCircle,
  Download,
  FileSpreadsheet,
  HelpCircle,
  Loader2,
  Trash,
  Upload,
  Users,
  XCircle,
} from "lucide-react";
import { useCallback, useMemo, useState } from "react";
import { useDropzone } from "react-dropzone";
import { toast } from "sonner";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";

import { cn } from "@/lib/utils";
import { addMembersToGroup, importUsers, useGroups } from "@/services/admin";

// Interface for user data read from CSV file
interface UserImportData {
  name: string;
  email: string;
  group: string;
  groupId?: number;
  rowNumber: number;
  isValid: boolean;
  errorMessage?: string;
}

interface BulkImportDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function BulkImportDialog({
  open,
  onOpenChange,
}: BulkImportDialogProps) {
  const queryClient = useQueryClient();
  const { data: groups = [] } = useGroups();
  const [importStep, setImportStep] = useState(1);
  const [uploadedFileName, setUploadedFileName] = useState<string | null>(null);
  const [userImportData, setUserImportData] = useState<UserImportData[]>([]);
  const [isImporting, setIsImporting] = useState(false);
  const [importError, setImportError] = useState<string | null>(null);

  // Create a map of group names to ids for easy lookup
  const groupNameToIdMap = useMemo(() => {
    const map = new Map<string, number>();
    groups.forEach((group) => {
      map.set(group.name.toLowerCase(), group.id);
    });
    return map;
  }, [groups]);

  const handleOnFileLoaded = async (file: File) => {
    try {
      const text = await file.text();
      const records = parse(text, {
        skip_empty_lines: true,
      }) as string[][];

      if (records.length > 1) {
        // Skip header row and process data
        const users: UserImportData[] = [];

        // Process each row, starting from row 1 (after header)
        for (let i = 1; i < records.length; i++) {
          const row = records[i];
          if (row && row.length >= 3) {
            const name = row[0]?.trim();
            const email = row[1]?.trim();
            const group = row[2]?.trim();

            // Validate email format
            const isValidEmail = email
              ? /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)
              : false;

            // Validate group exists - group is required
            const groupId = group
              ? groupNameToIdMap.get(group.toLowerCase())
              : undefined;
            const isValidGroup = groupId !== undefined;

            const isValid = Boolean(
              name && email && isValidEmail && isValidGroup,
            );

            users.push({
              name: name || "",
              email: email || "",
              group: group || "",
              groupId,
              rowNumber: i + 1,
              isValid,
              errorMessage: !isValid
                ? !name
                  ? "Thiếu tên"
                  : !email
                    ? "Thiếu email"
                    : !isValidEmail
                      ? "Email không hợp lệ"
                      : !group
                        ? "Thiếu Phòng ban"
                        : !isValidGroup
                          ? "Phòng ban không tồn tại"
                          : "Lỗi không xác định"
                : undefined,
            });
          } else if (row && row.length >= 2) {
            // Handle case when group column is not provided
            const name = row[0]?.trim();
            const email = row[1]?.trim();

            // Phòng ban là bắt buộc, nên đánh dấu là không hợp lệ
            users.push({
              name: name || "",
              email: email || "",
              group: "",
              groupId: undefined,
              rowNumber: i + 1,
              isValid: false,
              errorMessage: "Thiếu Phòng ban",
            });
          }
        }

        if (users.length > 0) {
          setUserImportData(users);
          setUploadedFileName(file.name);
          setImportStep(2);
        } else {
          toast.error("File không có dữ liệu hợp lệ", {
            description: "Vui lòng kiểm tra lại định dạng file.",
          });
        }
      } else {
        toast.error("File không có dữ liệu", {
          description: "File không chứa dữ liệu người dùng.",
        });
      }
    } catch (error) {
      toast.error("Lỗi đọc file CSV", {
        description:
          "Không thể đọc dữ liệu từ file. Vui lòng kiểm tra lại định dạng.",
      });
    }
  };

  const handleOnError = (fileRejections: FileRejection[]) => {
    toast.error("Lỗi đọc file CSV", {
      description:
        fileRejections[0]?.errors[0]?.message || "File không được chấp nhận.",
    });
  };

  // FileDropzone Component
  interface FileDropzoneProps {
    onFileAccepted: (file: File) => void;
    onFileRejected: (fileRejections: FileRejection[]) => void;
  }

  interface FileRejection {
    file: File;
    errors: {
      code: string;
      message: string;
    }[];
  }

  function FileDropzone({ onFileAccepted, onFileRejected }: FileDropzoneProps) {
    const onDrop = useCallback(
      (acceptedFiles: File[], fileRejections: FileRejection[]) => {
        if (acceptedFiles.length > 0) {
          onFileAccepted(acceptedFiles[0]);
        }

        if (fileRejections.length > 0) {
          onFileRejected(fileRejections);
        }
      },
      [onFileAccepted, onFileRejected],
    );

    const { getRootProps, getInputProps, isDragActive } = useDropzone({
      onDrop,
      accept: {
        "text/csv": [".csv"],
        "application/vnd.ms-excel": [".csv"],
      },
      maxFiles: 1,
      multiple: false,
    });

    return (
      <Card>
        <CardContent className="p-0">
          <div
            {...getRootProps()}
            className={cn(
              "flex flex-col items-center justify-center rounded-lg border-2 border-dashed p-8 text-center transition-all duration-200",
              isDragActive
                ? "border-primary bg-primary/5 shadow-md"
                : "border-gray-200 bg-gray-50/50 hover:border-primary/30 hover:bg-primary/5",
            )}
          >
            <input {...getInputProps()} />
            <div className="text-center">
              {isDragActive ? (
                <div className="animate-pulse">
                  <div className="mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-primary/10">
                    <Upload className="h-8 w-8 text-primary" />
                  </div>
                  <h3 className="font-medium text-lg text-primary">
                    Thả file vào đây
                  </h3>
                  <p className="mt-1 text-primary/70 text-sm">
                    Sẵn sàng để tải lên
                  </p>
                </div>
              ) : (
                <>
                  <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-gray-100">
                    <FileSpreadsheet className="h-8 w-8 text-gray-500" />
                  </div>
                  <h3 className="font-medium text-gray-700 text-lg">
                    Tải lên file CSV
                  </h3>
                  <p className="mt-2 text-gray-500 text-sm">
                    Kéo và thả file CSV vào đây hoặc
                  </p>
                  <Button
                    variant="ghost"
                    className="mt-2 bg-primary/5 text-primary hover:bg-primary/10"
                  >
                    Chọn file
                  </Button>
                  <p className="mt-3 text-gray-400 text-xs">
                    Chỉ hỗ trợ file CSV (*.csv)
                  </p>
                </>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  const downloadTemplate = () => {
    // Create CSV data
    const header = "Họ tên,Email,Phòng ban (bắt buộc)\n";
    const rows =
      "Nguyễn Văn A,<EMAIL>,Tên Phòng ban\nTrần Thị B,<EMAIL>,Tên Phòng ban";
    const csvContent = header + rows;

    // Create Blob from CSV data with BOM for Excel compatibility
    const blob = new Blob([new Uint8Array([0xef, 0xbb, 0xbf]), csvContent], {
      type: "text/csv;charset=utf-8;",
    });

    // Create URL and download link
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.setAttribute("href", url);
    link.setAttribute("download", "mau_import_nguoi_dung.csv");
    link.style.visibility = "hidden";

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const resetImport = (closeDialog = false) => {
    setImportStep(1);
    setUploadedFileName(null);
    setUserImportData([]);
    setImportError(null);

    if (closeDialog) {
      onOpenChange(false);
    }
  };

  // Remove a user from the import list
  const removeUserFromImport = (index: number) => {
    setUserImportData((prev) => prev.filter((_, i) => i !== index));
  };

  // Check if we can proceed to the confirmation step
  const canProceedToConfirm = () => {
    if (userImportData.length === 0) return false;

    // Check if there's at least one valid user
    return userImportData.some((user) => user.isValid);
  };

  const handleConfirmImport = async () => {
    try {
      setIsImporting(true);
      setImportError(null);

      // Filter valid users for import
      const validUsers = userImportData.filter((user) => user.isValid);

      if (validUsers.length === 0) {
        toast.error("Không có dữ liệu hợp lệ để import");
        setIsImporting(false);
        return;
      }

      // Call API to import users
      const importedUsers = await importUsers(
        validUsers.map((user) => ({
          name: user.name,
          email: user.email,
        })),
      );

      // Group users by group_id for batch processing
      const usersByGroup = new Map<number, string[]>();

      // Map imported users to their specified groups
      if (importedUsers && importedUsers.length > 0) {
        importedUsers.forEach((user, index) => {
          const importData = validUsers[index];
          if (importData.groupId && user.id) {
            const groupId = importData.groupId;
            const userId = user.id;

            if (!usersByGroup.has(groupId)) {
              usersByGroup.set(groupId, []);
            }

            usersByGroup.get(groupId)?.push(userId);
          }
        });

        // Add users to their respective groups
        const addToGroupPromises = Array.from(usersByGroup.entries()).map(
          ([groupId, userIds]) => {
            return addMembersToGroup({
              group_id: String(groupId),
              user_ids: userIds,
            });
          },
        );

        // Wait for all group assignments to complete
        if (addToGroupPromises.length > 0) {
          await Promise.all(addToGroupPromises);
        }
      }

      // Update users and groups list data
      queryClient.invalidateQueries({ queryKey: ["admin-users"] });
      queryClient.invalidateQueries({ queryKey: ["admin-groups"] });

      toast.success(`Đã import thành công ${validUsers.length} người dùng`);
      resetImport(true); // Close dialog after successful import
    } catch (error) {
      console.error("Import error:", error);
      setImportError("Đã xảy ra lỗi khi import người dùng. Vui lòng thử lại.");
      toast.error("Lỗi import người dùng", {
        description: "Đã xảy ra lỗi khi import người dùng. Vui lòng thử lại.",
      });
    } finally {
      setIsImporting(false);
    }
  };
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-6xl">
        <DialogHeader>
          <DialogTitle>Nhập người dùng hàng loạt</DialogTitle>
          <DialogDescription>
            Upload file CSV để thêm nhiều người dùng cùng lúc
          </DialogDescription>
        </DialogHeader>

        <Tabs value={`step-${importStep}`} className="w-full">
          <TabsList className="grid w-full grid-cols-3 p-1">
            <TabsTrigger
              value="step-1"
              disabled={importStep !== 1}
              className={cn(
                "flex items-center gap-2 data-[state=active]:bg-primary data-[state=active]:text-white",
                importStep > 1 ? "text-green-600" : "",
              )}
            >
              {importStep > 1 ? <CheckCircle className="h-4 w-4" /> : "1."}{" "}
              Upload file
            </TabsTrigger>
            <TabsTrigger
              value="step-2"
              disabled={importStep !== 2}
              className={cn(
                "flex items-center gap-2 data-[state=active]:bg-primary data-[state=active]:text-white",
                importStep > 2 ? "text-green-600" : "",
              )}
            >
              {importStep > 2 ? <CheckCircle className="h-4 w-4" /> : "2."} Xem
              trước
            </TabsTrigger>
            <TabsTrigger
              value="step-3"
              disabled={importStep !== 3}
              className="flex items-center gap-2 data-[state=active]:bg-primary data-[state=active]:text-white"
            >
              3. Xác nhận
            </TabsTrigger>
          </TabsList>

          {/* Step 1: Upload */}
          <TabsContent value="step-1" className="space-y-4">
            <div className="mt-6 space-y-4">
              {/* File upload area with drag & drop */}
              <FileDropzone
                onFileAccepted={handleOnFileLoaded}
                onFileRejected={handleOnError}
              />

              {/* Template download */}
              <div className="mt-6 flex items-center justify-center gap-4">
                <HelpCircle className="h-5 w-5 text-blue-500" />
                <div className="text-sm">
                  <p className="text-gray-700">
                    Chưa có file CSV? Bạn có thể tải mẫu ở đây.
                  </p>
                </div>
                <Button
                  variant="outline"
                  onClick={downloadTemplate}
                  className="ml-auto"
                >
                  <Download className="mr-2 h-4 w-4" />
                  Tải template mẫu
                </Button>
              </div>
            </div>
          </TabsContent>

          {/* Step 2: Preview */}
          <TabsContent value="step-2" className="space-y-4">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-medium">Xem trước dữ liệu</h3>
                  <p className="text-gray-500 text-sm">
                    File: {uploadedFileName}
                  </p>
                </div>
                <div className="flex gap-2">
                  {userImportData.some((u) => u.isValid) && (
                    <Badge
                      variant="outline"
                      className="border-green-200 bg-green-50 text-green-700"
                    >
                      <CheckCircle className="mr-1 h-3 w-3" />
                      {userImportData.filter((u) => u.isValid).length} hợp lệ
                    </Badge>
                  )}
                  {userImportData.some((u) => !u.isValid) && (
                    <Badge
                      variant="outline"
                      className="border-red-200 bg-red-50 text-red-700"
                    >
                      <XCircle className="mr-1 h-3 w-3" />
                      {userImportData.filter((u) => !u.isValid).length} không
                      hợp lệ
                    </Badge>
                  )}
                </div>
              </div>

              {userImportData.length > 0 ? (
                <div className="overflow-hidden rounded-md border">
                  {userImportData.some((u) => !u.isValid) && (
                    <div className="border-amber-100 border-b bg-amber-50 p-3 text-amber-800 text-sm">
                      <div className="flex items-center">
                        <AlertCircle className="mr-2 h-4 w-4" />
                        <p>
                          Có {userImportData.filter((u) => !u.isValid).length}{" "}
                          người dùng không hợp lệ cần xem xét lại.
                        </p>
                      </div>
                    </div>
                  )}
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-[50px]">STT</TableHead>
                        <TableHead>Họ tên</TableHead>
                        <TableHead>Email</TableHead>
                        <TableHead>Phòng ban</TableHead>
                        <TableHead className="w-[150px]">Trạng thái</TableHead>
                        <TableHead className="w-[250px]">
                          Thông báo lỗi
                        </TableHead>
                        <TableHead className="w-[60px]"></TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {userImportData.map((user, index) => (
                        <TableRow
                          key={index}
                          className={!user.isValid ? "bg-red-50/30" : ""}
                        >
                          <TableCell>{index + 1}</TableCell>
                          <TableCell>{user.name}</TableCell>
                          <TableCell className="max-w-md overflow-hidden text-ellipsis">
                            {user.email}
                          </TableCell>
                          <TableCell>{user.group || "-"}</TableCell>
                          <TableCell>
                            {user.isValid ? (
                              <div className="flex items-center">
                                <Badge
                                  variant="outline"
                                  className="border-green-200 bg-green-50 text-green-700"
                                >
                                  <CheckCircle className="mr-1 h-3 w-3" />
                                  Hợp lệ
                                </Badge>
                              </div>
                            ) : (
                              <Badge
                                variant="outline"
                                className="border-red-200 bg-red-50 text-red-700"
                              >
                                <XCircle className="mr-1 h-3 w-3" />
                                Không hợp lệ
                              </Badge>
                            )}
                          </TableCell>
                          <TableCell>
                            {!user.isValid && (
                              <span className="text-red-600 text-sm">
                                {user.errorMessage}
                              </span>
                            )}
                          </TableCell>
                          <TableCell>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => removeUserFromImport(index)}
                              className="hover:bg-red-50 hover:text-red-600"
                            >
                              <Trash className="h-4 w-4" />
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              ) : (
                <div className="rounded-lg bg-amber-50 p-4">
                  <p className="text-amber-800 text-sm">
                    Không có dữ liệu người dùng hợp lệ trong file. Vui lòng kiểm
                    tra lại.
                  </p>
                </div>
              )}
            </div>
          </TabsContent>

          {/* Step 3: Confirm */}
          <TabsContent value="step-3" className="space-y-4">
            <div className="space-y-4">
              <Card className="overflow-hidden">
                <CardContent>
                  <div className="flex flex-col items-center justify-center text-center">
                    <div className="mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-primary/10">
                      <Users className="h-5 w-5 text-primary" />
                    </div>
                    <h3 className="font-medium text-gray-800 text-xl">
                      Sẵn sàng import
                    </h3>
                    <div className="mt-2 flex gap-2">
                      {userImportData.some((u) => u.isValid) && (
                        <Badge
                          variant="outline"
                          className="border-green-200 bg-green-50 text-green-700"
                        >
                          <CheckCircle className="mr-1 h-3 w-3" />
                          {userImportData.filter((u) => u.isValid).length} hợp
                          lệ
                        </Badge>
                      )}
                      {userImportData.some((u) => !u.isValid) && (
                        <Badge
                          variant="outline"
                          className="border-red-200 bg-red-50 text-red-700"
                        >
                          <XCircle className="mr-1 h-3 w-3" />
                          {userImportData.filter((u) => !u.isValid).length}{" "}
                          không hợp lệ
                        </Badge>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>

              {importError && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>Lỗi</AlertTitle>
                  <AlertDescription>{importError}</AlertDescription>
                </Alert>
              )}

              <Alert className="border-blue-200 bg-blue-50">
                <HelpCircle className="h-4 w-4 text-blue-600" />
                <AlertTitle className="text-blue-700">Lưu ý</AlertTitle>
                <AlertDescription className="text-blue-600">
                  Hệ thống sẽ chỉ import những người dùng có dữ liệu hợp lệ.
                  {userImportData.some((u) => !u.isValid) && (
                    <span> Người dùng không hợp lệ sẽ bị bỏ qua.</span>
                  )}
                </AlertDescription>
              </Alert>

              {userImportData.some((u) => !u.isValid) && (
                <Card className="mt-6 border-red-200">
                  <CardContent className="p-0">
                    <div className="border-red-100 border-b bg-red-50 px-4 py-3">
                      <h4 className="flex items-center font-medium text-red-800">
                        <XCircle className="mr-2 h-4 w-4" />
                        Danh sách người dùng không hợp lệ (
                        {userImportData.filter((u) => !u.isValid).length})
                      </h4>
                    </div>
                    <div className="overflow-hidden">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead className="w-[50px]">STT</TableHead>
                            <TableHead>Họ tên</TableHead>
                            <TableHead>Email</TableHead>
                            <TableHead>Phòng ban</TableHead>
                            <TableHead className="w-[250px]">
                              Thông báo lỗi
                            </TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {userImportData
                            .filter((user) => !user.isValid)
                            .map((user, index) => (
                              <TableRow
                                key={index}
                                className="hover:bg-red-50/50"
                              >
                                <TableCell>{index + 1}</TableCell>
                                <TableCell>{user.name || "—"}</TableCell>
                                <TableCell className="max-w-md overflow-hidden text-ellipsis">
                                  {user.email || "—"}
                                </TableCell>
                                <TableCell>{user.group || "—"}</TableCell>
                                <TableCell>
                                  <span className="text-red-600 text-sm">
                                    {user.errorMessage}
                                  </span>
                                </TableCell>
                              </TableRow>
                            ))}
                        </TableBody>
                      </Table>
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          </TabsContent>
        </Tabs>

        <DialogFooter>
          {importStep === 1 && (
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              Hủy
            </Button>
          )}

          {importStep === 2 && (
            <>
              <Button variant="ghost" onClick={() => resetImport(false)}>
                <Upload className="mr-2 h-4 w-4" />
                Chọn file khác
              </Button>
              <Button
                onClick={() => setImportStep(3)}
                disabled={!canProceedToConfirm()}
                className="bg-primary hover:bg-primary/90"
              >
                Tiếp tục
              </Button>
            </>
          )}

          {importStep === 3 && (
            <>
              <Button
                variant="ghost"
                onClick={() => setImportStep(2)}
                disabled={isImporting}
              >
                Quay lại
              </Button>
              <Button
                onClick={handleConfirmImport}
                disabled={isImporting}
                className="bg-primary hover:bg-primary/90"
              >
                {isImporting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Đang xử lý...
                  </>
                ) : (
                  "Bắt đầu import"
                )}
              </Button>
            </>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
