import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>pDown } from "lucide-react";
import { useMemo, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { TooltipProvider } from "@/components/ui/tooltip";
import { useUsers } from "@/services/admin";
import { useGroups } from "@/services/admin";
import { User as ApiUser } from "@/types/auth";
import {
  User as UIUser,
  UserFiltersState,
  UserFilterType,
} from "@/types/users";
import formatDate from "@/utils/format-date";
import { DeleteUserDialog } from "./DeleteUserDialog";
import { DepartmentChangeDialog } from "./DepartmentChangeDialog";
import { PasswordResetDialog } from "./PasswordResetDialog";
import { StatusChangeDialog } from "./StatusChangeDialog";
import { UserFilters } from "./UserFilters";
import { UserTableRow } from "./UserTableRow";

interface UserTableProps {
  selectedUsers?: string[];
  onSelectedUsersChange?: (users: string[]) => void;
}

export function UserTable({
  selectedUsers: externalSelectedUsers,
  onSelectedUsersChange,
}: UserTableProps = {}) {
  const { data: apiUsers, isLoading, refetch: refetchUsers } = useUsers();
  const [internalSelectedUsers, setInternalSelectedUsers] = useState<string[]>(
    [],
  );
  const [sortColumn, setSortColumn] = useState<string>("joinDate");
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("desc");
  const [currentPage, setCurrentPage] = useState(1);
  const [deleteUserDialogOpen, setDeleteUserDialogOpen] = useState(false);
  const [userToDelete, setUserToDelete] = useState<{
    userId: string;
    user: UIUser;
  } | null>(null);

  // Add filter state
  const [filters, setFilters] = useState<UserFiltersState>({
    searchTerm: "",
    filterStatus: "all",
    filterDepartment: "all",
    filterRole: "all",
  });

  // Use either external or internal state based on what's provided
  const selectedUsers =
    externalSelectedUsers !== undefined
      ? externalSelectedUsers
      : internalSelectedUsers;
  const setSelectedUsers = onSelectedUsersChange || setInternalSelectedUsers;

  const [statusChangeDialogOpen, setStatusChangeDialogOpen] = useState(false);
  const [userToChangeStatus, setUserToChangeStatus] = useState<{
    userId: string;
    user: UIUser;
  } | null>(null);

  const [passwordResetDialogOpen, setPasswordResetDialogOpen] = useState(false);
  const [userToResetPassword, setUserToResetPassword] = useState<{
    userId: string;
    user: UIUser;
  } | null>(null);

  const [departmentChangeDialogOpen, setDepartmentChangeDialogOpen] =
    useState(false);
  const [userToChangeDepartment, setUserToChangeDepartment] = useState<{
    userId: string;
    user: UIUser;
  } | null>(null);
  const itemsPerPage = 10;

  // Get groups data for departments
  const { data: groups = [] } = useGroups();
  const departments = groups.map((group) => group.name).filter(Boolean);

  // Handle filter change
  const handleFilterChange = (type: UserFilterType, value: string) => {
    setFilters((prevFilters) => ({
      ...prevFilters,
      [type]: value,
    }));
    setCurrentPage(1); // Reset to page 1 when filters change
  };

  // Process users data and map API users to UI format
  const users = (apiUsers || []).map(
    (apiUser): UIUser => ({
      id: apiUser.id,
      name: apiUser.name,
      email: apiUser.email,
      // Map API data to UI format
      department: "", // Empty department as requested
      role: apiUser.is_instructor ? "instructor" : "student",
      status: "active", // Default to active
      lastLogin: apiUser.updated_at || "",
      joinDate: apiUser.created_at || "",
      avatar: apiUser.avatar_url || "",
      phone: "",
      manager: "",
      location: "",
      coursesEnrolled: 0,
      coursesCompleted: 0,
      averageScore: 0,
      lastActivity: apiUser.updated_at || "",
    }),
  );

  // Apply filters
  const filteredUsers = useMemo(() => {
    return users.filter((user) => {
      const matchesSearch =
        filters.searchTerm === "" ||
        user.name.toLowerCase().includes(filters.searchTerm.toLowerCase()) ||
        user.email.toLowerCase().includes(filters.searchTerm.toLowerCase());

      const matchesStatus =
        filters.filterStatus === "all" || user.status === filters.filterStatus;

      // Compare department name
      const matchesDepartment =
        filters.filterDepartment === "all" ||
        user.department === filters.filterDepartment;

      // Match role exactly - 'student' or 'instructor'
      const matchesRole =
        filters.filterRole === "all" || user.role === filters.filterRole;

      return matchesSearch && matchesStatus && matchesDepartment && matchesRole;
    });
  }, [users, filters]);

  // Custom sorting
  const sortedUsers = [...filteredUsers].sort((a, b) => {
    if (sortColumn === "name") {
      return sortDirection === "asc"
        ? a.name.localeCompare(b.name)
        : b.name.localeCompare(a.name);
    }
    if (sortColumn === "joinDate") {
      const dateA = new Date(a.joinDate).getTime();
      const dateB = new Date(b.joinDate).getTime();
      return sortDirection === "asc" ? dateA - dateB : dateB - dateA;
    }
    return 0;
  });

  // Calculate pagination
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;

  const totalUsers = users.length;
  const currentPageUsers = sortedUsers.slice(startIndex, endIndex);

  const handleSort = (column: string) => {
    if (sortColumn === column) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortColumn(column);
      setSortDirection("asc");
    }
  };

  const handleOpenDeleteDialog = (userId: string, user: UIUser) => {
    setUserToDelete({ userId, user });
    setDeleteUserDialogOpen(true);
  };

  const handleOpenStatusChangeDialog = (userId: string, user: UIUser) => {
    setUserToChangeStatus({ userId, user });
    setStatusChangeDialogOpen(true);
  };

  const handleOpenPasswordResetDialog = (userId: string, user: UIUser) => {
    setUserToResetPassword({ userId, user });
    setPasswordResetDialogOpen(true);
  };

  const handleOpenDepartmentChangeDialog = (userId: string, user: UIUser) => {
    setUserToChangeDepartment({ userId, user });
    setDepartmentChangeDialogOpen(true);
  };

  // Handle pagination
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Handle selection
  const handleSelectAll = (checked: boolean) => {
    const newSelectedUsers = checked
      ? currentPageUsers.map((user) => user.id.toString())
      : [];

    if (onSelectedUsersChange) {
      onSelectedUsersChange(newSelectedUsers);
      return;
    }

    setInternalSelectedUsers(newSelectedUsers);
  };

  const handleSelectUser = (userId: string, checked: boolean) => {
    if (checked) {
      if (onSelectedUsersChange) {
        onSelectedUsersChange([...selectedUsers, userId]);
        return;
      }
      setInternalSelectedUsers([...internalSelectedUsers, userId]);
      return;
    }

    // Not checked
    if (onSelectedUsersChange) {
      onSelectedUsersChange(selectedUsers.filter((id) => id !== userId));
      return;
    }
    setInternalSelectedUsers(
      internalSelectedUsers.filter((id) => id !== userId),
    );
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>Danh sách người dùng ({totalUsers})</CardTitle>
          {/* Items per page selector moved to pagination */}
        </div>
      </CardHeader>
      <CardContent>
        {/* Filters */}
        <div className="mb-6">
          <UserFilters
            filters={filters}
            departments={departments}
            onFilterChange={handleFilterChange}
          />
        </div>
        {isLoading ? (
          <div className="flex items-center justify-center p-8">
            <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
            <span className="ml-2">Đang tải dữ liệu...</span>
          </div>
        ) : (
          <TooltipProvider>
            <div className="overflow-hidden rounded-md border">
              <Table>
                <TableHeader className="bg-gray-50">
                  <TableRow>
                    <TableHead className="w-12">
                      <Checkbox
                        checked={
                          selectedUsers.length === currentPageUsers.length &&
                          currentPageUsers.length > 0
                        }
                        onCheckedChange={handleSelectAll}
                      />
                    </TableHead>
                    <TableHead>
                      <div
                        className="flex cursor-pointer items-center"
                        onClick={() => handleSort("name")}
                      >
                        Tên người dùng
                        {sortColumn === "name" ? (
                          sortDirection === "asc" ? (
                            <ArrowUp className="ml-1 h-4 w-4" />
                          ) : (
                            <ArrowDown className="ml-1 h-4 w-4" />
                          )
                        ) : (
                          <ArrowUpDown className="ml-1 h-4 w-4 opacity-50" />
                        )}
                      </div>
                    </TableHead>
                    <TableHead>Vai trò</TableHead>
                    <TableHead>Trạng thái</TableHead>
                    <TableHead>Phòng ban</TableHead>
                    <TableHead>
                      <div
                        className="flex cursor-pointer items-center"
                        onClick={() => handleSort("joinDate")}
                      >
                        Ngày tham gia
                        {sortColumn === "joinDate" ? (
                          sortDirection === "asc" ? (
                            <ArrowUp className="ml-1 h-4 w-4" />
                          ) : (
                            <ArrowDown className="ml-1 h-4 w-4" />
                          )
                        ) : (
                          <ArrowUpDown className="ml-1 h-4 w-4 opacity-50" />
                        )}
                      </div>
                    </TableHead>
                    <TableHead>Thao tác</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {currentPageUsers.map((user) => (
                    <UserTableRow
                      key={user.id}
                      user={user}
                      isSelected={selectedUsers.includes(user.id.toString())}
                      onSelect={handleSelectUser}
                      onDelete={handleOpenDeleteDialog}
                      onToggleStatus={handleOpenStatusChangeDialog}
                      onResetPassword={handleOpenPasswordResetDialog}
                      onChangeDepartment={handleOpenDepartmentChangeDialog}
                    />
                  ))}

                  {currentPageUsers.length === 0 && (
                    <TableRow>
                      <TableCell colSpan={6} className="py-6 text-center">
                        Không có dữ liệu người dùng nào
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>
          </TooltipProvider>
        )}

        {/* Custom pagination implementation */}
        {!isLoading && totalUsers > 0 && (
          <div className="mt-4 flex items-center justify-between">
            <div className="text-muted-foreground text-sm">
              Hiển thị {startIndex + 1}-{Math.min(endIndex, totalUsers)} của{" "}
              {totalUsers} người dùng
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                disabled={currentPage === 1}
                onClick={() => handlePageChange(currentPage - 1)}
              >
                Trước
              </Button>
              {Array.from(
                { length: Math.min(5, Math.ceil(totalUsers / itemsPerPage)) },
                (_, i) => (
                  <Button
                    key={i + 1}
                    variant={currentPage === i + 1 ? "default" : "outline"}
                    size="sm"
                    onClick={() => handlePageChange(i + 1)}
                  >
                    {i + 1}
                  </Button>
                ),
              )}
              <Button
                variant="outline"
                size="sm"
                disabled={currentPage >= Math.ceil(totalUsers / itemsPerPage)}
                onClick={() => handlePageChange(currentPage + 1)}
              >
                Sau
              </Button>
            </div>
          </div>
        )}
      </CardContent>

      {/* Delete User Dialog */}
      {userToDelete && (
        <DeleteUserDialog
          open={deleteUserDialogOpen}
          onOpenChange={setDeleteUserDialogOpen}
          userId={userToDelete.userId}
          user={userToDelete.user}
        />
      )}

      {/* Status Change Dialog */}
      {userToChangeStatus && (
        <StatusChangeDialog
          open={statusChangeDialogOpen}
          onOpenChange={setStatusChangeDialogOpen}
          userId={userToChangeStatus.userId}
          user={userToChangeStatus.user}
        />
      )}

      {/* Password Reset Dialog */}
      {userToResetPassword && (
        <PasswordResetDialog
          open={passwordResetDialogOpen}
          onOpenChange={setPasswordResetDialogOpen}
          userId={userToResetPassword.userId}
          user={userToResetPassword.user}
        />
      )}

      {/* Department Change Dialog */}
      {userToChangeDepartment && (
        <DepartmentChangeDialog
          open={departmentChangeDialogOpen}
          onOpenChange={(open) => {
            setDepartmentChangeDialogOpen(open);
            // Refresh users data when the dialog is closed
            if (!open) {
              setTimeout(() => {
                // Add a small delay to allow the API changes to propagate
                refetchUsers();
              }, 500);
            }
          }}
          userId={userToChangeDepartment.userId}
          user={userToChangeDepartment.user}
        />
      )}
    </Card>
  );
}
