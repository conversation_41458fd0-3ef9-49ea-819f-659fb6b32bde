import { useEffect, useState } from "react";
import { toast } from "sonner";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  Di<PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { addMembersToGroup, useGroups } from "@/services/admin";
import { User } from "@/types/users";

interface DepartmentChangeDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  userId?: string;
  user?: User;
}

export function DepartmentChangeDialog({
  open,
  onOpenChange,
  userId,
  user,
}: DepartmentChangeDialogProps) {
  const { data: groups = [] } = useGroups();
  const [selectedGroupId, setSelectedGroupId] = useState<string>("");
  const [isSaving, setIsSaving] = useState(false);

  // Set initial group if user already belongs to one
  useEffect(() => {
    if (user && open) {
      // Try to match the user's department name with available groups
      const matchingGroup = groups.find(
        (group) => group.name.toLowerCase() === user.department.toLowerCase(),
      );

      if (matchingGroup) {
        setSelectedGroupId(String(matchingGroup.id));
      } else {
        setSelectedGroupId("");
      }
    }
  }, [user, groups, open]);

  // Handle form submission
  const handleConfirm = async () => {
    if (!userId || !selectedGroupId) return;
    if (!user) return;

    try {
      setIsSaving(true);

      // Call API to add user to the selected group
      await addMembersToGroup({
        group_id: selectedGroupId,
        user_ids: [userId],
      });

      // Get the group name for display
      const selectedGroup = groups.find(
        (g) => g.id.toString() === selectedGroupId,
      );

      toast.success(`Đã cập nhật phòng ban cho ${user.name} thành công`, {
        description: `Đã thêm người dùng vào nhóm ${selectedGroup?.name || selectedGroupId}`,
      });

      onOpenChange(false);
      return;
    } catch (error) {
      console.error("Error updating department:", error);
      toast.error("Không thể cập nhật phòng ban", {
        description: "Đã xảy ra lỗi khi thực hiện thao tác này.",
      });
      return;
    } finally {
      setIsSaving(false);
    }
  };

  // Reset form when dialog opens
  const handleOpenChange = (isOpen: boolean) => {
    onOpenChange(isOpen);
  };

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>Thay đổi phòng ban</DialogTitle>
          <DialogDescription>
            Gán hoặc thay đổi phòng ban cho {user?.name}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="department">Phòng ban</Label>
            <Select value={selectedGroupId} onValueChange={setSelectedGroupId}>
              <SelectTrigger>
                <SelectValue placeholder="Chọn phòng ban" />
              </SelectTrigger>
              <SelectContent>
                {groups.length === 0 ? (
                  <SelectItem value="no-groups" disabled>
                    Không có phòng ban nào
                  </SelectItem>
                ) : (
                  groups.map((group) => (
                    <SelectItem key={group.id} value={String(group.id)}>
                      {group.name}
                    </SelectItem>
                  ))
                )}
              </SelectContent>
            </Select>
          </div>
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isSaving}
          >
            Hủy
          </Button>
          <Button
            onClick={handleConfirm}
            disabled={isSaving || !selectedGroupId}
          >
            {isSaving ? "Đang lưu..." : "Lưu"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
