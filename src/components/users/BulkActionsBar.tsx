import { Button } from "@/components/ui/button";
import type { BulkActionType } from "@/store/users/types";

interface BulkActionsBarProps {
  selectedUsers: string[];
  onBulkAction: (action: BulkActionType, selectedCount: number) => void;
}

export function BulkActionsBar({
  selectedUsers,
  onBulkAction,
}: BulkActionsBarProps) {
  const selectedCount = selectedUsers.length;

  // Don't render if no users selected
  if (selectedCount === 0) return null;

  // Handle opening dialog for specific action
  const handleBulkAction = (action: BulkActionType) => {
    onBulkAction(action, selectedCount);
  };

  return (
    <div className="flex items-center gap-4 rounded-lg bg-blue-50 p-4">
      <span className="font-medium text-sm">
        <PERSON><PERSON> chọn {selectedCount} người dùng
      </span>
      <div className="flex gap-2">
        <Button
          variant="outline"
          size="sm"
          onClick={() => handleBulkAction("reset-password")}
        >
          Đặt lại mật khẩu
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => handleBulkAction("change-department")}
        >
          Thay đổi phòng ban
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => handleBulkAction("change-status")}
        >
          Thay đổi trạng thái
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => handleBulkAction("delete")}
          className="text-red-600 hover:text-red-700"
        >
          Xóa người dùng
        </Button>
      </div>
    </div>
  );
}
