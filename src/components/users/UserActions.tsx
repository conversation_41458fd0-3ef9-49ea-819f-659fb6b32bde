import { useNavigate } from "@tanstack/react-router";
import {
  Building,
  Eye,
  KeyRound,
  MoreVertical,
  Trash2,
  UserCheck,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import type { User } from "@/types/users";

interface UserActionsProps {
  userId: string;
  user: User; // Pass the full user object to access properties like name, status, etc.
  onDelete?: (userId: string, user: User) => void; // Optional callback for delete action
  onToggleStatus?: (userId: string, user: User) => void; // Optional callback for status change
  onResetPassword?: (userId: string, user: User) => void; // Optional callback for password reset
  onChangeDepartment?: (userId: string, user: User) => void; // Optional callback for department change
}

export function UserActions({
  userId,
  user,
  onDelete,
  onToggleStatus: toggleStatusCallback,
  onResetPassword: resetPasswordCallback,
  onChangeDepartment: changeDepartmentCallback,
}: UserActionsProps) {
  const navigate = useNavigate();

  // Action handlers
  const onViewDetails = (userId: string) => {
    navigate({ to: `/dashboard/users/${userId}` });
  };

  const onToggleStatus = () => {
    if (toggleStatusCallback) {
      toggleStatusCallback(userId, user);
    } else {
      console.warn("No toggleStatus callback provided for user:", user.name);
    }
  };

  const onResetPassword = () => {
    if (resetPasswordCallback) {
      resetPasswordCallback(userId, user);
    } else {
      console.warn("No resetPassword callback provided for user:", user.name);
    }
  };

  const onChangeDepartment = () => {
    if (changeDepartmentCallback) {
      changeDepartmentCallback(userId, user);
    } else {
      console.warn(
        "No changeDepartment callback provided for user:",
        user.name,
      );
    }
  };

  const onDeleteUser = () => {
    if (onDelete) {
      onDelete(userId, user);
    } else {
      console.warn("No delete callback provided for user:", user.name);
    }
  };
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
          <MoreVertical className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem onClick={() => onViewDetails(userId)}>
          <Eye className="mr-2 h-4 w-4" />
          Xem chi tiết
        </DropdownMenuItem>
        <DropdownMenuItem onClick={onToggleStatus}>
          <UserCheck className="mr-2 h-4 w-4" />
          Đổi trạng thái
        </DropdownMenuItem>
        <DropdownMenuItem onClick={onResetPassword}>
          <KeyRound className="mr-2 h-4 w-4" />
          Reset password
        </DropdownMenuItem>
        <DropdownMenuItem onClick={onChangeDepartment}>
          <Building className="mr-2 h-4 w-4" />
          Gán / chỉnh sửa phòng ban
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem
          onClick={onDeleteUser}
          className="text-red-600 focus:text-red-600"
        >
          <Trash2 className="mr-2 h-4 w-4" />
          Xóa người dùng
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
