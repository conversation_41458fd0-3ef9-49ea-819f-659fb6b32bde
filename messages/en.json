{"$schema": "https://inlang.com/schema/inlang-message-format", "courseEditor": {"labels": {"createTitle": "Create a new course", "namePlaceholder": "Name", "slugPlaceholder": "Slug", "imageUrlPlaceholder": "Image URL", "categoriesPlaceholder": "Select categories...", "studentLevelPlaceholder": "Student level", "languagePlaceholder": "Language", "overviewLabel": "What you'll learn:", "overviewPlaceholder": "Overview...", "goalsLabel": "Course goals", "goalPlaceholder": "Goal", "addGoalButton": "Add new goal", "publishedLabel": "Published", "unpublishedLabel": "Unpublished", "cancelButton": "Cancel", "saveButton": "Save", "uploadingText": "uploading..."}, "validation": {"invalidDescription": "invalid course description", "invalidField": "Please fill in the", "invalidCategories": "Please select at least one category", "uploadFileWarning": "Please select a file before uploading.", "saveSuccess": "Course saved successfully!", "saveFailed": "Failed to save course:"}}, "sidebar": {"organizationSettings": "Organization Settings", "userManagement": "User Management", "courseManagement": "Course Management", "wikiManagement": "Wiki Management", "analyticsReports": "Analytics & Reports", "collaborationLXP": "Collaboration & LXP", "settings": "Settings", "getHelp": "Get Help", "search": "Search", "dataLibrary": "Data Library", "reports": "Reports", "wordAssistant": "Word Assistant", "learningPlatform": "Learning platform"}, "language": "Language", "login": "<PERSON><PERSON>", "courseStats": {"totalStudents": "Total Students", "studentsLabel": "Students", "totalStudentsTitle": "Total Students", "totalStudentsSubtitle": "Number of students enrolled in the course", "totalLessons": "Total Lessons", "lessonsLabel": "Lessons", "totalLessonsTitle": "Total Lessons", "totalLessonsSubtitle": "Number of lessons in the course", "completionRate": "Completion Rate", "goodRate": "Good", "needsImprovement": "Needs Improvement", "goodRateTitle": "Good Rate", "needsImprovementTitle": "Needs Improvement", "completionRateSubtitle": "Percentage of students who completed the course", "rating": "Rating", "courseRatingTitle": "Course Rating", "ratingSubtitle": "reviews", "excellent": "Excellent", "good": "Good", "fair": "Fair"}, "groupSelect": {"placeholder": "Select group or leave empty", "searchPlaceholder": "Search group...", "noGroup": "No group", "noGroupFound": "No group found."}, "analytics": {"departmentStats": "Department Statistics", "departmentStatsSubtitle": "Performance overview by department", "department": "Department", "employees": "Employees", "completionPercent": "% Completion", "progress": "Progress", "avgXP": "Avg XP", "avgTime": "Avg Time"}, "quiz": {"editQuiz": "Edit quiz", "createNewQuiz": "Create a new quiz", "skippable": "Skippable", "question": "Question", "questionPlaceholder": "Enter your question...", "answers": "Answers", "setCorrectAnswer": "Set as correct answer", "removeAnswer": "Remove answer", "addAnswer": "Add Answer", "explanation": "Explanation", "invalidFields": "Invalid quiz fields!", "invalidAnswer": "Invalid quiz answer or correct answer!", "invalidAnswerContent": "Invalid quiz answer content!", "savedQuiz": "Saved quiz!", "saveQuizFailed": "Save quiz failed."}, "createCourse": {"createNew": "Create from scratch", "createNewDesc": "Create a completely new course", "importFromFile": "Import from file", "importFromFileDesc": "Upload content from existing file", "selectTemplate": "Select template", "selectTemplateDesc": "Use an existing template", "cancel": "Cancel"}, "common": {"published": "Published", "draft": "Draft", "goBack": "Go back", "backToList": "Back to list"}, "courseFilters": {"statusAll": "All statuses", "statusPlaceholder": "Select status", "levelAll": "All levels", "levelPlaceholder": "Select level", "languageAll": "All languages", "languagePlaceholder": "Select language"}, "courseList": {"pageTitle": "Course List", "pageSubtitle": "Manage and view all courses here", "title": "Courses", "displayLabel": "Show", "itemsLabel": "items", "resultsLabel": "Results", "table": {"courseName": "Course Name", "instructor": "<PERSON><PERSON><PERSON><PERSON>", "status": "Status", "level": "Level", "language": "Language", "creationDate": "Created At", "actions": "Actions"}, "empty": {"title": "No courses yet", "description": "Start by creating your first course to manage learning content.", "notFound": "No courses found", "notFoundDesc": "No courses match your current filters. Try adjusting your filters.", "createButton": "Create your first course"}, "create": {"button": "Create new course", "title": "Create new course", "description": "Choose how to create a course"}}, "students": {"registeredStudents": "Registered Students", "currentProgressDesc": "Current students list and their learning progress", "searchPlaceholder": "Search students...", "columns": {"student": "Student", "department": "Department", "enrollDate": "Enrolled Date", "progress": "Progress", "status": "Status", "score": "Score", "studyTime": "Study Time", "lastAccess": "Last Access"}, "score": {"points": "points"}, "paging": {"of": "of"}}, "courseDetail": {"tabs": {"overview": "Overview", "stats": "Statistics", "content": "Content", "permissions": "Permissions", "students": "Students", "history": "History"}, "overview": {"courseInfo": "Course Information", "editButton": "Edit", "courseName": "Course Name", "urlSlug": "URL Slug", "level": "Level", "language": "Language", "status": "Status", "instructor": "<PERSON><PERSON><PERSON><PERSON>", "createdDate": "Created Date", "updatedDate": "Last Updated", "categories": "Categories", "courseImage": "Course Image", "courseDescription": "Course Description", "overview": "Overview:", "courseGoals": "Course Goals:", "statusDraft": "Draft", "statusPublished": "Published", "noInstructor": "Not assigned", "noCategories": "No categories"}}}